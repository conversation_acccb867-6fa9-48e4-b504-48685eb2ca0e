#include "controllers/ApiController.hpp"
#include "core/Application.hpp"
#include "services/AuthService.hpp"
#include <spdlog/spdlog.h>

ApiController::ApiController(Application &app)
    : app_(app)
{
    // Set CORS headers for all responses
    auto &server = app.getServer();
    server.set_pre_routing_handler([](const httplib::Request &req, httplib::Response &res) {
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        return httplib::Server::HandlerResponse::Unhandled;
    });

    // Handle OPTIONS requests for CORS
    server.Options(".*", [](const httplib::Request &req, httplib::Response &res) { return; });

    // Register API routes with authentication
    using namespace std::placeholders;
    server.Get("/api/products", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getProducts(r1, r2); }); 
    });
    server.Get("/api/projects", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getProjects(r1, r2); }); 
    });
    server.Get("/api/customers", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getCustomers(r1, r2); }); 
    });
    server.Get("/api/users", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getUsers(r1, r2); }); 
    });
    server.Get("/api/holidays", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getHolidays(r1, r2); }); 
    });
    server.Get(R"(/api/holidays/year/(\d+) )", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getHolidaysByYear(r1, r2); }); 
    });
    server.Get("/api/workhours/range", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getWorkHoursByRange(r1, r2); }); 
    });
    server.Post("/api/workhours/submit", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { submitWorkHour(r1, r2); }); 
    });
    server.Get("/api/statistics/workhours", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getWorkHourStatistics(r1, r2); }); 
    });
    server.Get("/api/sync/status", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getSyncStatus(r1, r2); }); 
    });
    server.Get("/api/db/tables", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getTables(r1, r2); }); 
    });
    server.Post("/api/db/query", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { executeDbQuery(r1, r2); }); 
    });
    server.Get(R"(/api/db/table/:table)", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getTableData(r1, r2); }); 
    });
    server.Get("/api/config", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { getConfig(r1, r2); }); 
    });
    server.Post("/api/config", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { saveConfig(r1, r2); }); 
    });
    server.Post("/api/restart", [this](auto &&req, auto &&rep) { 
        handleAuthenticatedRequest(req, rep, [this](auto &&r1, auto &&r2) { restartApplication(r1, r2); }); 
    });
}

bool ApiController::isAuthenticated(const httplib::Request &req)
{
    std::string token = getSessionToken(req);
    if (token.empty())
    {
        return false;
    }

    auto auth_service = app_.getAuthService();
    return auth_service && auth_service->validateSession(token);
}

std::string ApiController::getSessionToken(const httplib::Request &req)
{
    // Try to get token from cookie
    auto cookie_header = req.get_header_value("Cookie");
    if (!cookie_header.empty())
    {
        size_t pos = cookie_header.find("session_token=");
        if (pos != std::string::npos)
        {
            pos += 14; // length of "session_token="
            size_t end = cookie_header.find(';', pos);
            if (end == std::string::npos)
                end = cookie_header.length();
            return cookie_header.substr(pos, end - pos);
        }
    }

    return "";
}

void ApiController::handleAuthenticatedRequest(const httplib::Request &req, httplib::Response &res, 
                                              std::function<void(const httplib::Request&, httplib::Response&)> handler)
{
    if (!isAuthenticated(req))
    {
        auto response = createResponse(1, "Authentication required");
        jsonResponse(res, response, 401);
        return;
    }

    handler(req, res);
}
