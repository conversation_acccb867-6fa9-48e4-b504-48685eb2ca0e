<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Report System</title>
    <!-- Lunar calendar library -->
    <script src="https://cdn.jsdelivr.net/npm/lunar-javascript@1.6.12/lunar.min.js"></script>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            font-size: 13px;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #f4f6f8;
        }

        /* Container Layout */
        .container {
            min-height: 100vh;
            display: flex;
            background-color: #fff;
        }

        /* Left Sidebar Navigation */
        .nav-sidebar {
            width: 100px;
            background: #34495e;
            border-right: 1px solid #bdc3c7;
            border-radius: 0;
            display: flex;
            flex-direction: column;
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 100;
            padding-bottom: 6px;
        }

        .nav-header {
            padding: 11px 8px;
            border-bottom: 1px solid #2c3e50;
            background: #2c3e50;
            text-align: center;
            height:64px;
        }

        .nav-header h1 {
            font-size: 16px;
            font-weight: 600;
            color: #ecf0f1;
            margin: 0;
            line-height: 1.2;
        }

        .nav-tabs {
            display: flex;
            flex-direction: column;
            padding: 8px 0;
            flex: 1;
        }

        .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px 4px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            color: #95a5a6;
            text-align: center;
            margin: 1px 6px;
            text-decoration: none;
            min-height: 56px;
            line-height: 1.2;
        }

        .nav-tab:hover {
            color: #ecf0f1;
            background: #2c3e50;
        }

        .nav-tab.active {
            background: #3498db;
            color: #fff;
            font-weight: 600;
        }

        .nav-tab-icon {
            width: 20px;
            height: 20px;
            margin-bottom: 3px;
            flex-shrink: 0;
        }

        .nav-tab span {
            word-break: break-word;
            hyphens: auto;
            max-width: 100%;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: 100px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .tab-content {
            flex: 1;
            overflow: visible;
            background: #f4f6f8;
        }

        .section {
            background: #fff;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            overflow: hidden;
        }

        .section h2 {
            color: #2c3e50;
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            letter-spacing: -0.025em;
            background: #ecf0f1;
            padding: 10px 12px;
            height:64px;
        }

        #data-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .data-title-operator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Ensure title with operator has proper layout */
        #data-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        #data-title>span {
            flex: 1;
            min-width: 200px;
        }

        #data-table {
            padding: 10px;
        }

        /* Button Styles */
        .btn {
            padding: 8px 16px;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            background: #fff;
            color: #2c3e50;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-family: inherit;
        }

        .btn:hover {
            background: #ecf0f1;
            border-color: #3498db;
        }

        .btn-primary {
            background: #3498db;
            color: #fff;
            border-color: #3498db;
        }

        .btn-primary:hover {
            background: #2980b9;
            border-color: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: #fff;
            border-color: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
            border-color: #229954;
        }

        /* Data Table Styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            overflow: hidden;
            background: #fff;
            border: 1px solid #bdc3c7;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            line-height: 1.5;
            vertical-align: top;
            background-color: #f8f9fa;
            border: 1px solid #bdc3c7;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            user-select: none;
            position: relative;
            text-align: center;
        }

        .data-table th:hover {
            background: #ecf0f1;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        /* Sort Indicators */
        .sort-indicator {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #7f8c8d;
        }

        .sort-asc::after {
            content: "↑";
            color: #3498db;
            font-weight: bold;
        }

        .sort-desc::after {
            content: "↓";
            color: #3498db;
            font-weight: bold;
        }

        /* Weekly Statistics Table */
        .weekly-stats-table {
            table-layout: fixed;
            width: 100%;
        }

        .weekly-stats-table th,
        .weekly-stats-table td {
            padding: 8px 6px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Fixed width for User and Total columns */
        .weekly-stats-table th:first-child,
        .weekly-stats-table td:first-child {
            width: 80px;
            min-width: 80px;
        }

        .weekly-stats-table th:last-child,
        .weekly-stats-table td:last-child {
            width: 60px;
            min-width: 60px;
        }

        /* Holiday columns get fixed width */
        .weekly-stats-table th.holiday-column,
        .weekly-stats-table td.holiday-column {
            width: 100px !important;
            min-width: 100px !important;
        }

        .weekly-stats-table td {
            height: auto;
            vertical-align: top;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* Empty column styling - removed special width, use equal distribution */
        .weekly-stats-table th.empty-column {
            background: #ecf0f1;
        }

        .weekly-stats-table span.project-name {
            font-weight: bold;
        }

        .weekly-stats-table span.work-hour {
            font-weight: bold;
            color: #3498db;
        }

        /* Holiday and weekend styling */
        .weekly-stats-table .holiday-column {
            background-color: #e8e8e8 !important;
            color: #666;
        }

        .weekly-stats-table .holiday-column td {
            background-color: #e8e8e8 !important;
        }

        .weekly-stats-table .weekend-work-column {
            color: #dc3545 !important;
            font-weight: 600;
        }

        .calendar-header {
            text-align: center;
        }

        .calendar-header h2 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(4, minmax(360px, 1fr));
            gap: 10px;
            margin: 0 auto;
        }

        .month-card {
            background: #fff;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            overflow: hidden;
        }

        .month-header {
            background: #34495e;
            color: #fff;
            padding: 8px;
            text-align: center;
        }

        .month-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .month-calendar {
            padding: 8px;
        }

        .day-headers {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            margin-bottom: 5px;
        }

        .day-header {
            text-align: center;
            font-weight: 600;
            font-size: 12px;
            color: #2c3e50;
            padding: 5px 2px;
        }

        .days-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
        }

        .day-cell {
            aspect-ratio: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            position: relative;
            background: #fff;
        }

        .day-cell.empty {
            background: transparent;
            border: none;
        }

        .day-cell.holiday {
            background: #e8e8e8;
            color: #666;
        }

        .day-cell.weekend-work {
            color: #ff3545;
            font-weight: 600;
        }

        .day-cell.today {
            background: #3498db !important;
            color: #fff !important;
            font-weight: 600;
        }

        .day-cell.today .holiday-name {
            color: #fff !important;
        }

        .day-number {
            font-weight: 500;
            line-height: 1;
        }

        .holiday-name {
            font-size: 10px;
            color: #666;
            text-align: center;
            line-height: 1;
            margin-top: 2px;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .lunar-date {
            font-size: 10px;
            color: #999;
            text-align: center;
            line-height: 1;
            margin-top: 2px;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .day-cell.today .lunar-date {
            color: #fff !important;
        }

        /* Loading and Error States */
        .loading {
            text-align: center;
            color: #7f8c8d;
            padding: 40px;
            font-size: 16px;
            background: #f8f9fa;
            margin: 20px 0;
            border: 1px solid #bdc3c7;
        }

        .error {
            text-align: center;
            color: #e74c3c;
            padding: 40px;
            font-size: 16px;
            background: #fadbd8;
            border: 1px solid #e74c3c;
            margin: 20px 0;
        }

        /* Link Styles */
        .product-link,
        .customer-link {
            color: #3498db;
            text-decoration: none;
            cursor: pointer;
            font-weight: 500;
            border-bottom: 1px solid transparent;
        }

        .product-link:hover,
        .customer-link:hover {
            color: #2980b9;
            border-bottom-color: #2980b9;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(44, 62, 80, 0.8);
        }

        .modal-content {
            background: #fff;
            margin: 5% auto;
            padding: 0;
            width: 90%;
            max-width: 600px;
            position: relative;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            overflow: hidden;
        }

        .modal-content h3 {
            background: #34495e;
            color: #ecf0f1;
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .modal-content table {
            width: 100%;
            margin: 24px 0;
        }

        .modal-content table td {
            padding: 8px 12px;
            border-bottom: 1px solid #ecf0f1;
        }

        .modal-content table td:first-child {
            font-weight: 600;
            color: #2c3e50;
            width: 30%;
        }

        .close {
            color: #ecf0f1;
            float: right;
            font-size: 24px;
            font-weight: normal;
            cursor: pointer;
            line-height: 1;
            padding: 0 4px;
        }

        .close:hover {
            color: #fff;
        }

        /* Filter Styles */
        .filter-container {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
            padding: 20px;
            background: #f8f9fa;
            border: 1px solid #bdc3c7;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .filter-select {
            padding: 8px 16px;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            background: #fff;
            font-size: 14px;
            min-width: 140px;
            font-family: inherit;
        }

        .filter-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .title-filters {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .title-filters .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .title-filters .filter-group label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .title-filters .filter-select {
            padding: 6px 10px;
            border: 1px solid #e1e5e9;
            border-radius: 0;
            background: #fff;
            font-size: 13px;
            min-width: 100px;
            font-family: inherit;
        }

        .title-filters .filter-select:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Statistics Styles */
        .stats-controls {
            background: #f8f9fa;
            border: 1px solid #bdc3c7;
            padding: 24px;
            margin-bottom: 24px;
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .period-btn {
            font-size: 14px;
            padding: 10px 20px;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            background: #fff;
            color: #2c3e50;
            font-family: inherit;
            font-weight: 500;
            cursor: pointer;
        }

        .period-btn:hover {
            background: #ecf0f1;
            border-color: #3498db;
        }

        .period-btn.active {
            background: #3498db;
            color: #fff;
            border-color: #3498db;
        }

        /* Status Badge Styles */
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: #27ae60;
            color: #fff;
        }

        .status-inactive {
            background: #e74c3c;
            color: #fff;
        }

        /* Database link hover effect */
        a.nav-tab:hover {
            color: #ecf0f1 !important;
            background: #2c3e50 !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-sidebar {
                width: 80px;
            }

            .nav-tab {
                font-size: 9px;
                min-height: 48px;
                padding: 6px 2px;
            }

            .nav-tab-icon {
                width: 16px;
                height: 16px;
            }

            .main-content {
                margin-left: 80px;
            }

            .tab-content {
                padding: 16px;
            }

            .section h2 {
                font-size: 20px;
                padding: 16px 20px;
            }

            .title-filters {
                justify-content: center;
            }

            .stats-summary {
                grid-template-columns: 1fr;
            }

            .data-table th,
            .data-table td {
                padding: 8px 12px;
                font-size: 12px;
            }
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #ecf0f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #95a5a6;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #7f8c8d;
        }

        /* Animation for loading states */
        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .loading {
            animation: pulse 2s infinite;
        }

        .reports-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .reports-controls .btn {
            padding: 8px 16px;
            font-size: 13px;
            min-width: auto;
        }

        /* Responsive adjustments for reports header */
        @media (max-width: 768px) {
            .reports-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .reports-controls {
                justify-content: center;
                flex-wrap: wrap;
            }
        }

        /* Work Hour Management Styles */
        .work-hour-cell {
            position: relative;
            min-height: 20px;
        }

        .work-hour-content {
            padding-right: 20px; /* Make space for edit icon */
        }

        .work-hour-create-btn {
            background: none;
            border: none;
            color: #3498db;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            padding: 0;
            text-decoration: underline;
        }

        .work-hour-create-btn:hover {
            color: #2980b9;
        }

        .work-hour-edit-btn {
            position: absolute;
            top: 2px;
            right: 2px;
            background: none;
            border: none;
            color: #7f8c8d;
            cursor: pointer;
            padding: 2px;
            opacity: 0.7;
        }

        .work-hour-edit-btn:hover {
            color: #3498db;
            background: #ecf0f1;
            opacity: 1;
        }

        .work-hour-edit-btn svg {
            display: block;
        }

        /* Work Hour Panel Modal */
        .work-hour-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(44,62,80,0.8);
        }

        .work-hour-modal-content {
            background: #fff;
            margin: 5% auto;
            padding: 0;
            width: 90%;
            max-width: 600px;
            position: relative;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            overflow: hidden;
        }

        .work-hour-modal-header {
            background: #34495e;
            color: #ecf0f1;
            padding: 20px 24px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .work-hour-modal-close {
            color: #ecf0f1;
            font-size: 24px;
            font-weight: normal;
            cursor: pointer;
            line-height: 1;
            padding: 0 4px;
        }

        .work-hour-modal-close:hover {
            color: #fff;
        }

        .work-hour-modal-body {
            padding: 24px;
            min-height: 400px;
        }

        /* Work Hour Form Styles */
        .work-hour-form {
            max-width: 100%;
        }



        .work-records {
            margin-bottom: 20px;
        }

        .record-item {
            background: #f8f9fa;
            border: 1px solid #ecf0f1;
            border-radius: 0;
            padding: 16px;
            margin-bottom: 12px;
            position: relative;
        }

        .record-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 12px;
            align-items: center;
            margin-bottom: 12px;
        }

        .record-content {
            grid-column: 1 / -1;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            font-size: 14px;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        .work-content-textarea {
            width: 100%;
            min-height: 60px;
            resize: vertical;
        }

        .remove-record-btn {
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 0;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
        }

        .remove-record-btn:hover {
            background: #c0392b;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 16px;
            border-top: 1px solid #ecf0f1;
        }

        .add-record-btn {
            background: #27ae60;
            color: white;
            border: none;
            border-radius: 0;
            padding: 10px 16px;
            cursor: pointer;
            font-size: 14px;
        }

        .add-record-btn:hover {
            background: #229954;
        }

        .total-hours {
            font-weight: 600;
            color: #2c3e50;
        }

        .submit-work-hour-btn {
            background: #3498db;
            color: white;
            border: none;
            border-radius: 0;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .submit-work-hour-btn:hover {
            background: #2980b9;
        }

        .submit-work-hour-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }

        .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 4px;
        }

        .success-message {
            color: #27ae60;
            font-size: 12px;
            margin-top: 4px;
        }

        /* Project Search Input Styles */
        .project-search-container {
            position: relative;
        }

        .project-search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 0;
            font-size: 14px;
            font-family: inherit;
        }

        .project-search-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .project-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #bdc3c7;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .project-dropdown-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #ecf0f1;
            font-size: 14px;
        }

        .project-dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .project-dropdown-item:last-child {
            border-bottom: none;
        }

        .project-dropdown-item.selected {
            background-color: #3498db;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Left Sidebar Navigation -->
        <div class="nav-sidebar">
            <div class="nav-header">
                <h1>Daily Report</h1>
            </div>
            <div class="nav-tabs">
                <button class="nav-tab" id="tab-statistics" onclick="selectTab('statistics', this)">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z">
                        </path>
                    </svg>
                    <span>Reports</span>
                </button>
                <button class="nav-tab" id="tab-user" onclick="selectTab('user', this)">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z">
                        </path>
                    </svg>
                    <span>Users</span>
                </button>
                <button class="nav-tab" id="tab-project" onclick="selectTab('project', this)">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <span>Projects</span>
                </button>
                <button class="nav-tab" id="tab-product" onclick="selectTab('product', this)">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM6 9.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm2.5 5a2.5 2.5 0 100-5 2.5 2.5 0 000 5z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <span>Products</span>
                </button>
                <button class="nav-tab" id="tab-customer" onclick="selectTab('customer', this)">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z">
                        </path>
                    </svg>
                    <span>Customers</span>
                </button>
            </div>
            <div>
                <a href="#" class="nav-tab" onclick="loadCalendarTool(); return false;">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Calendar</span>
                </a>
                <a href="#" class="nav-tab" onclick="loadDatabaseTool(); return false;">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
                        </path>
                    </svg>
                    <span>Database</span>
                </a>
                <a href="#" class="nav-tab" onclick="loadConfigTool(); return false;">
                    <svg class="nav-tab-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Settings</span>
                </a>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <div class="tab-content">
                <div class="section">
                    <h2 id="data-title">Select a tab to view data</h2>
                    <div id="data-table">
                        <p>Click on a tab in the sidebar to view the corresponding data.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for details -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <!-- Work Hour Management Modal -->
    <div id="workHourModal" class="work-hour-modal">
        <div class="work-hour-modal-content">
            <div class="work-hour-modal-header">
                <span id="workHourModalTitle">Work Hour Management</span>
                <span class="work-hour-modal-close" onclick="closeWorkHourPanel()">&times;</span>
            </div>
            <div class="work-hour-modal-body">
                <div class="work-hour-form">
                    <div class="work-records" id="workRecords">
                        <!-- Work records will be dynamically added here -->
                    </div>

                    <div class="form-actions">
                        <button class="add-record-btn" onclick="addWorkRecord()">Add Record</button>
                        <div class="total-hours" id="totalHours">Total: 0 hours</div>
                        <button class="submit-work-hour-btn" id="submitWorkHourBtn" onclick="submitWorkHour()">Submit Work Hours</button>
                    </div>

                    <div id="workHourMessage"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedDataType = null;
        let syncStatusData = {};
        let productsData = [];
        let customersData = [];
        let projectsData = [];
        let usersData = [];
        let currentTableData = [];
        let originalTableData = [];
        let sortConfig = { column: null, direction: 'asc' };
        let filterConfig = { product: '', customer: '' };


        async function fetchData(url) {
            try {
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Fetch error:', error);
                return { code: 1, message: error.message };
            }
        }

        async function loadReferenceData() {
            try {
                // Load products data
                const productsResult = await fetchData('/api/products');
                if (productsResult.code === 0 && productsResult.data) {
                    productsData = productsResult.data;
                }

                // Load customers data
                const customersResult = await fetchData('/api/customers');
                if (customersResult.code === 0 && customersResult.data) {
                    customersData = customersResult.data;
                }

                // Load projects data
                const projectsResult = await fetchData('/api/projects');
                if (projectsResult.code === 0 && projectsResult.data) {
                    projectsData = projectsResult.data;
                }

                // Load users data
                const usersResult = await fetchData('/api/users');
                if (usersResult.code === 0 && usersResult.data) {
                    usersData = usersResult.data;


                }
            } catch (error) {
                console.error('Error loading reference data:', error);
            }
        }

        function getProductById(id) {
            return productsData.find(p => p.id == id);
        }

        function getCustomerById(id) {
            return customersData.find(c => c.id == id);
        }

        function getProjectById(id) {
            return projectsData.find(p => p.id == id);
        }

        function getUserById(id) {
            return usersData.find(u => u.id == id);
        }

        function getUserByName(name) {
            return usersData.find(u => u.name == name);
        }



        function getCurrentWeekRange() {
            // Get current time in UTC+8
            const now = new Date();
            const utc8Now = new Date(now.getTime() + (8 * 60 * 60 * 1000));

            const dayOfWeek = utc8Now.getUTCDay();
            const startOfWeek = new Date(utc8Now);
            // Get Monday of current week (dayOfWeek: 0=Sunday, 1=Monday, ..., 6=Saturday)
            startOfWeek.setUTCDate(utc8Now.getUTCDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1));

            // Apply week offset
            startOfWeek.setUTCDate(startOfWeek.getUTCDate() + (currentWeekOffset * 7));
            startOfWeek.setUTCHours(0, 0, 0, 0);

            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setUTCDate(startOfWeek.getUTCDate() + 6); // Sunday of the same week
            endOfWeek.setUTCHours(23, 59, 59, 999);

            return {
                start: startOfWeek.toISOString().split('T')[0] + ' 00:00:00',
                end: endOfWeek.toISOString().split('T')[0] + ' 23:59:59',
                startDate: startOfWeek.toISOString().split('T')[0],
                endDate: endOfWeek.toISOString().split('T')[0]
            };
        }

        function getCurrentMonthRange() {
            // Get current time in UTC+8
            const now = new Date();
            const utc8Now = new Date(now.getTime() + (8 * 60 * 60 * 1000));

            const startOfMonth = new Date(Date.UTC(utc8Now.getUTCFullYear(), utc8Now.getUTCMonth(), 1));
            const endOfMonth = new Date(Date.UTC(utc8Now.getUTCFullYear(), utc8Now.getUTCMonth() + 1, 0));

            return {
                start: startOfMonth.toISOString().split('T')[0],
                end: endOfMonth.toISOString().split('T')[0]
            };
        }
        function showModal(content) {
            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('detailsModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('detailsModal').style.display = 'none';
        }

        function showProductDetails(productId) {
            const product = getProductById(productId);
            if (product) {
                const details = `
                    <h3>${product.name || 'Product'} Details</h3>
                    <table >
                        <tr><td ><strong>ID:</strong></td><td >${product.id}</td></tr>
                        <tr><td ><strong>Name:</strong></td><td >${product.name || ''}</td></tr>
                        <tr><td ><strong>Description:</strong></td><td >${product.description || ''}</td></tr>
                        <tr><td ><strong>Start Time:</strong></td><td >${product.begin_time || ''}</td></tr>
                        <tr><td ><strong>End Time:</strong></td><td >${product.end_time || ''}</td></tr>
                        <tr><td ><strong>Group:</strong></td><td >${product.group_name || ''}</td></tr>
                    </table>
                `;
                showModal(details);
            }
        }

        function showCustomerDetails(customerId) {
            const customer = getCustomerById(customerId);
            if (customer) {
                const details = `
                    <h3>${customer.unit_name || 'Customer'} Details</h3>
                    <table >
                        <tr><td ><strong>ID:</strong></td><td >${customer.id}</td></tr>
                        <tr><td ><strong>Unit Name:</strong></td><td >${customer.unit_name || ''}</td></tr>
                        <tr><td ><strong>Contact:</strong></td><td >${customer.contact || ''}</td></tr>
                        <tr><td ><strong>Department:</strong></td><td >${customer.department || ''}</td></tr>
                        <tr><td ><strong>Phones:</strong></td><td >${customer.phones || ''}</td></tr>
                        <tr><td ><strong>Group:</strong></td><td >${customer.group_name || ''}</td></tr>
                    </table>
                `;
                showModal(details);
            }
        }

        // Handle ESC key to close modals and dropdowns
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('detailsModal');
                const workHourModal = document.getElementById('workHourModal');

                // Close details modal if it's open
                if (modal && modal.style.display === 'block') {
                    closeModal();
                    return;
                }

                // Close work hour modal if it's open
                if (workHourModal && workHourModal.style.display === 'block') {
                    closeWorkHourPanel();
                    return;
                }

                // Close any open project dropdowns
                const allDropdowns = document.querySelectorAll('.project-dropdown');
                allDropdowns.forEach(dropdown => {
                    if (dropdown.style.display === 'block') {
                        dropdown.style.display = 'none';
                    }
                });
            }
        });

        // Work Hour Management Functions
        let workRecordCounter = 0;
        let currentWorkRecords = [];

        // Work types mapping
        const WORK_TYPES = {
            0: 'Management',
            1: 'Meeting',
            2: 'Development',
            3: 'Testing',
            4: 'Operations'
        };

        function openWorkHourPanel(mode, userName, dateStr) {
            const modal = document.getElementById('workHourModal');
            const title = document.getElementById('workHourModalTitle');

            // Format date and get day of week
            const date = new Date(dateStr);
            const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const dayOfWeek = dayNames[date.getDay()];

            if (mode === 'create') {
                title.textContent = `Work Hour Registration - ${userName} (${dateStr} ${dayOfWeek})`;
                currentWorkRecords = [];
                addWorkRecord(); // Add one empty record by default
            } else if (mode === 'edit') {
                title.textContent = `Work Hour Edit - ${userName} (${dateStr} ${dayOfWeek})`;
                loadExistingWorkRecords(userName, dateStr);
            }

            // Store current context for later use
            window.currentWorkHourContext = {
                mode: mode,
                userName: userName,
                dateStr: dateStr
            };

            updateTotalHours();
            modal.style.display = 'block';
        }

        function closeWorkHourPanel() {
            const modal = document.getElementById('workHourModal');
            modal.style.display = 'none';
            window.currentWorkHourContext = null;
            currentWorkRecords = [];
            workRecordCounter = 0;
            syncCheckAttempts = 0; // Reset sync check counter
            document.getElementById('workRecords').innerHTML = '';
            document.getElementById('workHourMessage').innerHTML = '';

            // Reset button states
            restoreButtonStates();
        }

        function addWorkRecord() {
            workRecordCounter++;
            const recordId = `record_${workRecordCounter}`;

            // Calculate smart default times based on existing records
            const defaultTimes = calculateDefaultTimes();

            const recordHtml = `
                <div class="record-item" id="${recordId}">
                    <div class="record-row">
                        <div class="project-search-container">
                            <input type="text" class="form-control project-search-input"
                                   placeholder="Search project name..."
                                   onkeyup="handleProjectSearchKeyup(event, this, '${recordId}')"
                                   onfocus="showProjectDropdown('${recordId}')"
                                   onblur="hideProjectDropdown('${recordId}')" />
                            <div class="project-dropdown" id="${recordId}_dropdown"></div>
                            <input type="hidden" class="project-id" value="" />
                        </div>
                        <select class="form-control start-time" onchange="onTimeChange('${recordId}', false)">
                            ${generateTimeOptionsWithDefault(defaultTimes.start)}
                        </select>
                        <select class="form-control end-time" onchange="onTimeChange('${recordId}', true)">
                            ${generateTimeOptionsWithDefault(defaultTimes.end)}
                        </select>
                        <button class="remove-record-btn" onclick="removeWorkRecord('${recordId}')">Remove</button>
                    </div>
                    <div class="record-content">
                        <textarea class="form-control work-content-textarea"
                                  placeholder="Enter work description..."
                                  oninput="updateTotalHours()"></textarea>
                        <div class="error-message" id="${recordId}_error"></div>
                    </div>
                </div>
            `;

            document.getElementById('workRecords').insertAdjacentHTML('beforeend', recordHtml);
            updateTotalHours();
        }

        // Calculate smart default times for new records
        function calculateDefaultTimes() {
            const existingRecords = document.querySelectorAll('.record-item');

            // If no existing records, use 09:00-18:00
            if (existingRecords.length === 0) {
                return { start: 9, end: 18 };
            }

            // Get all occupied time ranges
            const occupiedRanges = [];
            existingRecords.forEach(record => {
                const startTime = record.querySelector('.start-time').value;
                const endTime = record.querySelector('.end-time').value;

                if (startTime && endTime) {
                    const startHour = parseInt(startTime.split(':')[0]);
                    const endHour = parseInt(endTime.split(':')[0]);

                    if (startHour < endHour) {
                        occupiedRanges.push({ start: startHour, end: endHour });
                    }
                }
            });

            // Sort ranges by start time
            occupiedRanges.sort((a, b) => a.start - b.start);

            // Find the largest available time slot
            const availableSlots = [];

            // Check slot before first range
            if (occupiedRanges.length > 0 && occupiedRanges[0].start > 9) {
                availableSlots.push({ start: 9, end: occupiedRanges[0].start });
            }

            // Check slots between ranges
            for (let i = 0; i < occupiedRanges.length - 1; i++) {
                const currentEnd = occupiedRanges[i].end;
                const nextStart = occupiedRanges[i + 1].start;

                if (nextStart > currentEnd) {
                    availableSlots.push({ start: currentEnd, end: nextStart });
                }
            }

            // Check slot after last range
            if (occupiedRanges.length > 0) {
                const lastEnd = occupiedRanges[occupiedRanges.length - 1].end;
                if (lastEnd < 18) {
                    availableSlots.push({ start: lastEnd, end: 18 });
                }
            }

            // If no occupied ranges, return full day
            if (occupiedRanges.length === 0) {
                return { start: 9, end: 18 };
            }

            // Find the largest available slot
            let largestSlot = { start: 9, end: 9, duration: 0 };
            availableSlots.forEach(slot => {
                const duration = slot.end - slot.start;
                if (duration > largestSlot.duration) {
                    largestSlot = { start: slot.start, end: slot.end, duration: duration };
                }
            });

            // If no available slots or largest slot is too small, use the end of last range
            if (largestSlot.duration === 0 || largestSlot.duration < 1) {
                const lastEnd = occupiedRanges[occupiedRanges.length - 1].end;
                return { start: Math.min(lastEnd, 17), end: 18 };
            }

            return { start: largestSlot.start, end: largestSlot.end };
        }

        // Generate time options with specific default selection
        function generateTimeOptionsWithDefault(defaultHour) {
            let options = '';
            for (let hour = 9; hour <= 18; hour++) {
                const timeStr = `${hour.toString().padStart(2, '0')}:00`;
                const selected = hour === defaultHour ? 'selected' : '';
                options += `<option value="${timeStr}" ${selected}>${timeStr}</option>`;
            }
            return options;
        }

        // Handle keyboard events for project search input
        function handleProjectSearchKeyup(event, input, recordId) {
            if (event.key === 'Escape') {
                // Close dropdown and clear focus
                const dropdown = document.getElementById(`${recordId}_dropdown`);
                dropdown.style.display = 'none';
                input.blur();
                return;
            }

            // Handle normal search
            searchProjects(input, recordId);
        }

        // Handle time selection change
        function onTimeChange(recordId, isEndTime) {
            const record = document.getElementById(recordId);
            const startSelect = record.querySelector('.start-time');
            const endSelect = record.querySelector('.end-time');

            // Update the companion time selector for the current record
            if (isEndTime) {
                // End time changed, update start time options for this record
                updateTimeOptions(startSelect, recordId, false);
            } else {
                // Start time changed, update end time options for this record
                updateTimeOptions(endSelect, recordId, true);
            }

            // Always update other records when any time changes
            // This ensures all records show correct available options
            setTimeout(() => {
                updateAllTimeOptions(recordId);
                updateTotalHours();
            }, 50); // Small delay to ensure current change is processed
        }

        // Update time options for all records
        function updateAllTimeOptions(excludeRecordId = null) {
            const records = document.querySelectorAll('.record-item');

            records.forEach(record => {
                if (excludeRecordId && record.id === excludeRecordId) {
                    return; // Skip the record that just changed
                }

                const startSelect = record.querySelector('.start-time');
                const endSelect = record.querySelector('.end-time');

                updateTimeOptions(startSelect, record.id, false);
                updateTimeOptions(endSelect, record.id, true);
            });
        }

        function removeWorkRecord(recordId) {
            const recordElement = document.getElementById(recordId);
            if (recordElement) {
                recordElement.remove();
                updateAllTimeOptions(); // Update time options for remaining records
                updateTotalHours();
            }
        }

        function generateTimeOptions(defaultHour = 9, recordId = null, isEndTime = false) {
            let options = '';
            for (let hour = 9; hour <= 18; hour++) {
                const timeStr = `${hour.toString().padStart(2, '0')}:00`;
                const selected = hour === defaultHour ? 'selected' : '';
                options += `<option value="${timeStr}" ${selected}>${timeStr}</option>`;
            }
            return options;
        }

        // Get all occupied time ranges excluding the current record
        function getOccupiedTimeRanges(excludeRecordId = null) {
            const occupiedRanges = [];
            const records = document.querySelectorAll('.record-item');

            records.forEach(record => {
                if (excludeRecordId && record.id === excludeRecordId) {
                    return; // Skip current record
                }

                const startTime = record.querySelector('.start-time').value;
                const endTime = record.querySelector('.end-time').value;

                if (startTime && endTime) {
                    const startHour = parseInt(startTime.split(':')[0]);
                    const endHour = parseInt(endTime.split(':')[0]);

                    if (startHour < endHour) {
                        occupiedRanges.push({ start: startHour, end: endHour });
                    }
                }
            });

            return occupiedRanges;
        }

        // Check if a time range conflicts with existing ranges
        function hasTimeConflict(startHour, endHour, excludeRecordId = null) {
            const occupiedRanges = getOccupiedTimeRanges(excludeRecordId);

            for (const range of occupiedRanges) {
                // Check for overlap: new range starts before existing ends AND new range ends after existing starts
                if (startHour < range.end && endHour > range.start) {
                    return true;
                }
            }

            return false;
        }

        // Update time options for a specific select element
        function updateTimeOptions(selectElement, recordId, isEndTime = false) {
            const currentValue = selectElement.value;
            const occupiedRanges = getOccupiedTimeRanges(recordId);

            // Get the current record's other time value (before clearing options)
            const record = selectElement.closest('.record-item');
            const otherTimeSelect = isEndTime ? record.querySelector('.start-time') : record.querySelector('.end-time');
            const otherTimeValue = otherTimeSelect ? otherTimeSelect.value : '';

            // Clear existing options
            selectElement.innerHTML = '';

            for (let hour = 9; hour <= 18; hour++) {
                const timeStr = `${hour.toString().padStart(2, '0')}:00`;
                let isAvailable = true;

                if (isEndTime) {
                    // For end time, check constraints
                    if (otherTimeValue) {
                        const startHour = parseInt(otherTimeValue.split(':')[0]);
                        if (hour <= startHour) {
                            isAvailable = false; // End time must be after start time
                        } else if (hasTimeConflict(startHour, hour, recordId)) {
                            isAvailable = false; // Would conflict with existing ranges
                        }
                    } else {
                        // No start time selected, check if this end hour conflicts with any range
                        for (const range of occupiedRanges) {
                            if (hour > range.start && hour <= range.end) {
                                isAvailable = false;
                                break;
                            }
                        }
                    }
                } else {
                    // For start time, check constraints
                    if (otherTimeValue) {
                        const endHour = parseInt(otherTimeValue.split(':')[0]);
                        if (hour >= endHour) {
                            isAvailable = false; // Start time must be before end time
                        } else if (hasTimeConflict(hour, endHour, recordId)) {
                            isAvailable = false; // Would conflict with existing ranges
                        }
                    } else {
                        // No end time selected, check if this start hour conflicts with any range
                        for (const range of occupiedRanges) {
                            if (hour >= range.start && hour < range.end) {
                                isAvailable = false;
                                break;
                            }
                        }
                    }
                }

                const option = document.createElement('option');
                option.value = timeStr;
                option.textContent = timeStr;
                option.disabled = !isAvailable;

                if (timeStr === currentValue) {
                    if (isAvailable) {
                        option.selected = true;
                    } else {
                        // Current value is no longer available, but don't clear it yet
                        // Let the user see the conflict and decide
                        option.selected = true;
                        option.style.color = 'red';
                        option.style.fontWeight = 'bold';
                    }
                }

                selectElement.appendChild(option);
            }

            // Restore the current value if it was cleared
            if (currentValue && selectElement.value !== currentValue) {
                selectElement.value = currentValue;
            }
        }

        function searchProjects(input, recordId) {
            const searchTerm = input.value.toLowerCase();
            const dropdown = document.getElementById(`${recordId}_dropdown`);

            if (searchTerm.length === 0) {
                dropdown.style.display = 'none';
                return;
            }

            const filteredProjects = projectsData.filter(project =>
                project.project_name.toLowerCase().includes(searchTerm)
            );

            let dropdownHtml = '';
            if (filteredProjects.length === 0) {
                dropdownHtml = '<div class="project-dropdown-item">No matching projects</div>';
            } else {
                filteredProjects.slice(0, 10).forEach(project => { // Limit to 10 results
                    dropdownHtml += `
                        <div class="project-dropdown-item"
                             onmousedown="selectProject('${recordId}', '${project.id}', '${project.project_name.replace(/'/g, "\\'")}')">
                            ${project.project_name}
                        </div>
                    `;
                });
            }

            dropdown.innerHTML = dropdownHtml;
            dropdown.style.display = 'block';
        }

        function showProjectDropdown(recordId) {
            const input = document.querySelector(`#${recordId} .project-search-input`);
            if (input.value.length > 0) {
                searchProjects(input, recordId);
            }
        }

        function hideProjectDropdown(recordId) {
            setTimeout(() => {
                const dropdown = document.getElementById(`${recordId}_dropdown`);
                dropdown.style.display = 'none';
            }, 200); // Delay to allow click on dropdown item
        }

        function selectProject(recordId, projectId, projectName) {
            const input = document.querySelector(`#${recordId} .project-search-input`);
            const hiddenInput = document.querySelector(`#${recordId} .project-id`);
            const dropdown = document.getElementById(`${recordId}_dropdown`);

            input.value = projectName;
            hiddenInput.value = projectId;
            dropdown.style.display = 'none';

            updateTotalHours();
        }

        function generateTimeSlots(startTime, endTime) {
            const start = parseInt(startTime.split(':')[0]);
            const end = parseInt(endTime.split(':')[0]);
            const slots = [];

            for (let i = start; i < end; i++) {
                if (i !== 12) { // Skip lunch hour
                    slots.push(`${i}:30`, `${i + 1}:00`);
                }
            }
            return slots;
        }

        function calculateMinutes(timeSlots) {
            return timeSlots.length * 30;
        }

        function updateTotalHours() {
            const records = document.querySelectorAll('.record-item');
            let totalMinutes = 0;
            let hasErrors = false;
            let hasValidRecords = false;

            records.forEach(record => {
                const startTime = record.querySelector('.start-time').value;
                const endTime = record.querySelector('.end-time').value;
                const content = record.querySelector('.work-content-textarea').value.trim();
                const projectInput = record.querySelector('.project-search-input').value.trim();
                const errorDiv = record.querySelector('.error-message');

                errorDiv.textContent = '';

                // Check if this record has any content
                const hasContent = content.length > 0;
                const hasProject = projectInput.length > 0;
                const hasTime = startTime && endTime;

                if (hasContent || hasProject || hasTime) {
                    // This record is being used, validate it completely
                    let recordErrors = [];

                    // Project is optional, no validation needed

                    if (!hasContent) {
                        recordErrors.push('Please enter work description');
                    }

                    if (!hasTime) {
                        recordErrors.push('Please select time');
                    } else {
                        const startHour = parseInt(startTime.split(':')[0]);
                        const endHour = parseInt(endTime.split(':')[0]);

                        if (startHour >= endHour) {
                            recordErrors.push('End time must be greater than start time');
                        } else if (hasTimeConflict(startHour, endHour, record.id)) {
                            recordErrors.push('Time slot conflicts with other records');
                        }
                    }

                    if (recordErrors.length > 0) {
                        errorDiv.textContent = recordErrors.join('，');
                        hasErrors = true;
                    } else {
                        // Valid record
                        const timeSlots = generateTimeSlots(startTime, endTime);
                        const minutes = calculateMinutes(timeSlots);
                        totalMinutes += minutes;
                        hasValidRecords = true;
                    }
                }
            });

            // Check total hours constraint
            if (totalMinutes > 8 * 60) {
                hasErrors = true;
                document.getElementById('workHourMessage').innerHTML =
                    '<div class="error-message">Total work hours cannot exceed 8 hours</div>';
            } else if (totalMinutes === 0 && hasValidRecords === false) {
                document.getElementById('workHourMessage').innerHTML =
                    '<div class="error-message">Please add at least one complete work record</div>';
            } else {
                document.getElementById('workHourMessage').innerHTML = '';
            }

            const totalHours = (totalMinutes / 60).toFixed(1);
            document.getElementById('totalHours').textContent = `Total: ${totalHours} hours`;

            // Enable submit button only if:
            // 1. No errors
            // 2. Has at least one valid record
            // 3. Total time > 0
            const submitBtn = document.getElementById('submitWorkHourBtn');
            submitBtn.disabled = hasErrors || !hasValidRecords || totalMinutes === 0;
        }

        function collectWorkRecords() {
            const records = [];
            const recordElements = document.querySelectorAll('.record-item');

            recordElements.forEach(record => {
                const projectId = record.querySelector('.project-id').value;
                const startTime = record.querySelector('.start-time').value;
                const endTime = record.querySelector('.end-time').value;
                const content = record.querySelector('.work-content-textarea').value.trim();

                if (startTime && endTime && content) {
                    const timeSlots = generateTimeSlots(startTime, endTime);
                    const minutes = calculateMinutes(timeSlots);
                    const now = Date.now();

                    // Determine work type: if no project selected, it's management type (0), otherwise development type (2)
                    const workType = projectId ? 2 : 0;

                    records.push({
                        submitComment: content,
                        projectId: projectId || "",
                        type: workType,
                        value: timeSlots,
                        submitWorkHour: minutes,
                        createTime: now,
                        updateTime: now
                    });
                }
            });

            return records;
        }

        function submitWorkHour() {
            const records = collectWorkRecords();
            if (records.length === 0) {
                document.getElementById('workHourMessage').innerHTML =
                    '<div class="error-message">Please add at least one work record</div>';
                return;
            }

            const submitBtn = document.getElementById('submitWorkHourBtn');

            // Disable all operation buttons and form inputs
            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';
            disableFormInputs();

            // Use the date from context
            const workDate = window.currentWorkHourContext.dateStr;
            const submitDate = new Date(workDate + 'T18:30:00').getTime();
            const now = Date.now();

            const workHourData = {
                createTime: now,
                updateTime: now,
                submitDate: submitDate,
                employeeId: "626782691571802528", // Guo Xuan's ID
                employeeName: "郭轩",
                timeConsumedList: records,
                tomorrowPlan: "",
                needCoordinateWork: ""
            };

            // Submit to API
            fetch('/api/workhours/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(workHourData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.code === 0) {
                    // Submit successful, start sync process
                    submitBtn.textContent = 'Syncing...';
                    document.getElementById('workHourMessage').innerHTML =
                        '<div class="success-message">Work hours submitted successfully! Syncing data...</div>';

                    // Wait for sync completion (backend automatically triggers sync after successful submitWorkHour)
                    // We need to check sync status to determine when it's complete
                    checkSyncCompletion();
                } else {
                    // Submit failed, restore button states
                    document.getElementById('workHourMessage').innerHTML =
                        `<div class="error-message">Submit failed: ${data.message}</div>`;
                    restoreButtonStates();
                }
            })
            .catch(error => {
                console.error('Submit error:', error);

                // Distinguish different types of errors and provide more accurate user guidance
                let errorMessage = 'Submit failed';
                const errorStr = error.message.toLowerCase();

                if (errorStr.includes('timeout') || errorStr.includes('408')) {
                    errorMessage = 'Request timeout, but data may have been submitted successfully. Please check work records or try again later.';
                } else if (errorStr.includes('network') || errorStr.includes('fetch')) {
                    errorMessage = 'Network connection error, please check your network connection and try again.';
                } else if (errorStr.includes('500')) {
                    errorMessage = 'Server internal error, please try again later or contact administrator.';
                } else if (errorStr.includes('401')) {
                    errorMessage = 'Authentication failed, trying to refresh authentication information...';
                } else if (errorStr.includes('403')) {
                    errorMessage = 'Insufficient permissions, please contact administrator.';
                } else {
                    errorMessage = `Submit failed: ${error.message}`;
                }

                document.getElementById('workHourMessage').innerHTML =
                    `<div class="error-message">${errorMessage}</div>`;

                // If it's a timeout error, suggest user to check data
                if (errorStr.includes('timeout')) {
                    setTimeout(() => {
                        document.getElementById('workHourMessage').innerHTML +=
                            '<div style="margin-top: 10px; font-size: 12px; color: #7f8c8d;">Suggestion: Close this window and check if work hours have been successfully submitted</div>';
                    }, 1000);
                }

                // Submit failed, restore button states
                restoreButtonStates();
            });
        }

        function restoreButtonStates() {
            const submitBtn = document.getElementById('submitWorkHourBtn');
            const addRecordBtn = document.querySelector('.add-record-btn');
            const removeRecordBtns = document.querySelectorAll('.remove-record-btn');

            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Submit Work Hours';
            }
            if (addRecordBtn) {
                addRecordBtn.disabled = false;
            }

            // Re-enable remove buttons
            removeRecordBtns.forEach(btn => {
                btn.disabled = false;
            });

            // Re-enable form inputs
            const inputs = document.querySelectorAll('#workRecords input, #workRecords select, #workRecords textarea');
            inputs.forEach(input => {
                input.disabled = false;
            });
        }

        function disableFormInputs() {
            const addRecordBtn = document.querySelector('.add-record-btn');
            const removeRecordBtns = document.querySelectorAll('.remove-record-btn');

            if (addRecordBtn) {
                addRecordBtn.disabled = true;
            }

            // Disable remove buttons
            removeRecordBtns.forEach(btn => {
                btn.disabled = true;
            });

            // Disable form inputs
            const inputs = document.querySelectorAll('#workRecords input, #workRecords select, #workRecords textarea');
            inputs.forEach(input => {
                input.disabled = true;
            });
        }

        let syncCheckAttempts = 0;
        const maxSyncCheckAttempts = 10; // Maximum 10 checks, about 30 seconds

        function checkSyncCompletion() {
            syncCheckAttempts++;

            // Check sync status
            fetch('/api/sync/status')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0 && data.data) {
                        const workHourStatus = data.data.find(status => status.data_type === 'workhour');

                        if (workHourStatus) {
                            // Check if sync time is recent (within time range after submission)
                            const syncTime = new Date(workHourStatus.sync_time).getTime();
                            const submitTime = Date.now();
                            const timeDiff = submitTime - syncTime;

                            // If sync time is within the last 5 minutes, consider sync complete
                            if (timeDiff < 5 * 60 * 1000) {
                                onSyncSuccess();
                                return;
                            }
                        }

                        // If sync is not complete yet, continue checking
                        if (syncCheckAttempts < maxSyncCheckAttempts) {
                            setTimeout(checkSyncCompletion, 3000); // Check again after 3 seconds
                        } else {
                            // Timeout, assume sync complete
                            onSyncSuccess();
                        }
                    } else {
                        // Failed to get sync status, continue trying
                        if (syncCheckAttempts < maxSyncCheckAttempts) {
                            setTimeout(checkSyncCompletion, 3000);
                        } else {
                            onSyncSuccess(); // Assume success after timeout
                        }
                    }
                })
                .catch(error => {
                    console.error('Sync status check error:', error);
                    // Check failed, continue trying
                    if (syncCheckAttempts < maxSyncCheckAttempts) {
                        setTimeout(checkSyncCompletion, 3000);
                    } else {
                        onSyncSuccess(); // Assume success after timeout
                    }
                });
        }

        function onSyncSuccess() {
            // Reset check counter
            syncCheckAttempts = 0;

            document.getElementById('workHourMessage').innerHTML =
                '<div class="success-message">Data sync completed! Closing panel...</div>';

            // Refresh statistics data
            if (selectedDataType === 'statistics') {
                loadStatistics();
            }

            // Delay closing panel to let user see success message
            setTimeout(() => {
                closeWorkHourPanel();

                // If currently on statistics page, refresh data to show new records
                if (selectedDataType === 'statistics') {
                    setTimeout(loadStatistics, 500);
                }
            }, 1500);
        }

        function loadExistingWorkRecords(userName, dateStr) {
            // This function would load existing work records for editing
            // For now, we'll add one empty record as placeholder
            currentWorkRecords = [];
            addWorkRecord();

            // TODO: Implement loading existing records from database
            // This would involve querying the work_hours table for the specific user and date
        }



        function sortTable(column, dataType) {
            // Toggle sort direction if clicking the same column
            if (sortConfig.column === column) {
                sortConfig.direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
            } else {
                sortConfig.column = column;
                sortConfig.direction = 'asc';
            }

            // Sort the data
            const sortedData = [...currentTableData].sort((a, b) => {
                let aValue = getColumnValue(a, column, dataType);
                let bValue = getColumnValue(b, column, dataType);

                // Handle numeric values
                if (!isNaN(aValue) && !isNaN(bValue)) {
                    aValue = Number(aValue);
                    bValue = Number(bValue);
                }

                // Handle string comparison
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }

                let result = 0;
                if (aValue < bValue) result = -1;
                else if (aValue > bValue) result = 1;

                return sortConfig.direction === 'desc' ? -result : result;
            });

            // Re-render the table with sorted data
            displayDataTable(sortedData, dataType, false);
        }

        function getColumnValue(item, column, dataType) {
            switch (dataType) {
                case 'product':
                    switch (column) {
                        case 'id': return item.id || 0;
                        case 'name': return item.name || '';
                        case 'description': return item.description || '';
                        default: return '';
                    }
                case 'user':
                    switch (column) {
                        case 'id': return item.id || '';
                        case 'account': return item.account || '';
                        case 'name': return item.name || '';
                        case 'dept_name': return item.dept_name || '';
                        case 'role_name': return item.role_name || '';
                        case 'enable': return item.enable ? 'Active' : 'Inactive';
                        default: return '';
                    }
                case 'project':
                    switch (column) {
                        case 'id': return item.id || 0;
                        case 'code': return item.project_code || '';
                        case 'name':
                            const projectName = item.project_name || '';
                            const shortName = item.short_name || '';
                            return shortName && shortName !== projectName ? `${projectName} / ${shortName}` : projectName;
                        case 'products':
                            // For sorting products, use the first product ID
                            if (item.rel_product_list) {
                                try {
                                    const products = typeof item.rel_product_list === 'string'
                                        ? JSON.parse(item.rel_product_list)
                                        : item.rel_product_list;
                                    if (Array.isArray(products) && products.length > 0) {
                                        return Number(products[0]) || 0;
                                    }
                                } catch (e) {
                                    if (typeof item.rel_product_list === 'string') {
                                        const productIds = item.rel_product_list.split(',').map(id => id.trim()).filter(id => id);
                                        if (productIds.length > 0) {
                                            return Number(productIds[0]) || 0;
                                        }
                                    }
                                }
                            }
                            return 0;
                        case 'customer':
                            const customer = getCustomerById(item.rel_customer_info);
                            return customer ? customer.unit_name : '';
                        case 'comment': return item.project_comment || '';
                        default: return '';
                    }
                case 'customer':
                    switch (column) {
                        case 'id': return item.id || 0;
                        case 'unit_name': return item.unit_name || '';
                        case 'group': return item.group_name || '';
                        default: return '';
                    }
                default:
                    return '';
            }
        }

        function generateSortableHeader(column, displayName, dataType) {
            const isCurrentSort = sortConfig.column === column;
            const sortClass = isCurrentSort ? (sortConfig.direction === 'asc' ? 'sort-asc' : 'sort-desc') : '';

            return `<th onclick="sortTable('${column}', '${dataType}')" class="${sortClass}">
                        ${displayName}
                        <span class="sort-indicator"></span>
                    </th>`;
        }

        function applyFilters() {
            let filteredData = [...originalTableData];

            // Apply product filter
            if (filterConfig.product) {
                filteredData = filteredData.filter(item => {
                    if (item.rel_product_list) {
                        try {
                            const products = typeof item.rel_product_list === 'string'
                                ? JSON.parse(item.rel_product_list)
                                : item.rel_product_list;
                            if (Array.isArray(products)) {
                                return products.includes(Number(filterConfig.product));
                            }
                        } catch (e) {
                            if (typeof item.rel_product_list === 'string') {
                                const productIds = item.rel_product_list.split(',').map(id => id.trim());
                                return productIds.includes(filterConfig.product);
                            }
                        }
                    }
                    return false;
                });
            }

            // Apply customer filter
            if (filterConfig.customer) {
                filteredData = filteredData.filter(item => {
                    return item.rel_customer_info == filterConfig.customer;
                });
            }

            // Update current data and re-render
            currentTableData = filteredData;
            displayDataTable(filteredData, selectedDataType, false);
        }

        function onProductFilterChange(selectElement) {
            filterConfig.product = selectElement.value;
            applyFilters();
        }

        function onCustomerFilterChange(selectElement) {
            filterConfig.customer = selectElement.value;
            applyFilters();
        }

        function clearFilters() {
            filterConfig = { product: '', customer: '' };
            const productFilter = document.getElementById('productFilter');
            const customerFilter = document.getElementById('customerFilter');
            if (productFilter) productFilter.value = '';
            if (customerFilter) customerFilter.value = '';
            currentTableData = [...originalTableData];
            displayDataTable(originalTableData, selectedDataType, false);
        }

        function generateFilterOptions() {
            if (selectedDataType !== 'project') return '';

            // Generate product options
            let productOptions = '<option value="">All Products</option>';
            const uniqueProducts = new Set();

            originalTableData.forEach(item => {
                if (item.rel_product_list) {
                    try {
                        const products = typeof item.rel_product_list === 'string'
                            ? JSON.parse(item.rel_product_list)
                            : item.rel_product_list;
                        if (Array.isArray(products)) {
                            products.forEach(productId => uniqueProducts.add(productId));
                        }
                    } catch (e) {
                        if (typeof item.rel_product_list === 'string') {
                            const productIds = item.rel_product_list.split(',').map(id => id.trim()).filter(id => id);
                            productIds.forEach(productId => uniqueProducts.add(productId));
                        }
                    }
                }
            });

            // Sort products by name (string sorting)
            const productList = Array.from(uniqueProducts).map(productId => {
                const product = getProductById(productId);
                const productName = product ? product.name : `ID:${productId}`;
                return { id: productId, name: productName };
            }).sort((a, b) => a.name.localeCompare(b.name));

            productList.forEach(item => {
                productOptions += `<option value="${item.id}">${item.name}</option>`;
            });

            // Generate customer options
            let customerOptions = '<option value="">All Customers</option>';
            const uniqueCustomers = new Set();

            originalTableData.forEach(item => {
                if (item.rel_customer_info) {
                    uniqueCustomers.add(item.rel_customer_info);
                }
            });

            // Sort customers by name (string sorting)
            const customerList = Array.from(uniqueCustomers).map(customerId => {
                const customer = getCustomerById(customerId);
                const customerName = customer ? customer.unit_name : `ID:${customerId}`;
                return { id: customerId, name: customerName };
            }).sort((a, b) => a.name.localeCompare(b.name));

            customerList.forEach(item => {
                customerOptions += `<option value="${item.id}">${item.name}</option>`;
            });

            return `
                <div class="filter-container">
                    <div class="filter-group">
                        <label for="productFilter">Product:</label>
                        <select id="productFilter" class="filter-select" onchange="onProductFilterChange(this)">
                            ${productOptions}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="customerFilter">Customer:</label>
                        <select id="customerFilter" class="filter-select" onchange="onCustomerFilterChange(this)">
                            ${customerOptions}
                        </select>
                    </div>
                    <button class="btn" onclick="clearFilters()">Clear Filters</button>
                </div>
            `;
        }

        function generateTitleFilterOptions(data = null) {
            // Use provided data or fallback to originalTableData
            const tableData = data || originalTableData || [];

            // Generate product options
            let productOptions = '<option value="">All Products</option>';
            const uniqueProducts = new Set();

            if (tableData && tableData.length > 0) {
                tableData.forEach(item => {
                    if (item.rel_product_list) {
                        try {
                            const products = typeof item.rel_product_list === 'string'
                                ? JSON.parse(item.rel_product_list)
                                : item.rel_product_list;
                            if (Array.isArray(products)) {
                                products.forEach(productId => uniqueProducts.add(productId));
                            }
                        } catch (e) {
                            if (typeof item.rel_product_list === 'string') {
                                const productIds = item.rel_product_list.split(',').map(id => id.trim()).filter(id => id);
                                productIds.forEach(productId => uniqueProducts.add(productId));
                            }
                        }
                    }
                });
            }

            // Sort products by name (string sorting)
            const productList = Array.from(uniqueProducts).map(productId => {
                const product = getProductById(productId);
                const productName = product ? product.name : `ID:${productId}`;
                return { id: productId, name: productName };
            }).sort((a, b) => a.name.localeCompare(b.name));

            productList.forEach(item => {
                productOptions += `<option value="${item.id}">${item.name}</option>`;
            });

            // Generate customer options
            let customerOptions = '<option value="">All Customers</option>';
            const uniqueCustomers = new Set();

            if (tableData && tableData.length > 0) {
                tableData.forEach(item => {
                    if (item.rel_customer_info) {
                        uniqueCustomers.add(item.rel_customer_info);
                    }
                });
            }

            // Sort customers by name (string sorting)
            const customerList = Array.from(uniqueCustomers).map(customerId => {
                const customer = getCustomerById(customerId);
                const customerName = customer ? customer.unit_name : `ID:${customerId}`;
                return { id: customerId, name: customerName };
            }).sort((a, b) => a.name.localeCompare(b.name));

            customerList.forEach(item => {
                customerOptions += `<option value="${item.id}">${item.name}</option>`;
            });

            return `
                <div class="filter-group">
                    <label for="productFilter">Product:</label>
                    <select id="productFilter" class="filter-select" onchange="onProductFilterChange(this)">
                        ${productOptions}
                    </select>
                </div>
                <div class="filter-group">
                    <label for="customerFilter">Customer:</label>
                    <select id="customerFilter" class="filter-select" onchange="onCustomerFilterChange(this)">
                        ${customerOptions}
                    </select>
                </div>
                <button class="btn" onclick="clearFilters()">Clear Filters</button>
            `;
        }

        function setTitleFilterValues() {
            // Set current filter values for title filters
            const productFilter = document.getElementById('productFilter');
            const customerFilter = document.getElementById('customerFilter');
            if (productFilter) productFilter.value = filterConfig.product;
            if (customerFilter) customerFilter.value = filterConfig.customer;
        }

        function selectTab(dataType, tabElement) {
            // Remove previous selection
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Select current tab
            tabElement.classList.add('active');
            selectedDataType = dataType;

            // Reset sort and filter configuration when switching data types
            sortConfig = { column: null, direction: 'asc' };
            filterConfig = { product: '', customer: '' };

            // Load corresponding data
            if (dataType === 'statistics') {
                loadStatistics();
            } else {
                loadDataByType(dataType);
            }
        }



        async function loadDataByType(dataType) {
            const titleElement = document.getElementById('data-title');
            const tableElement = document.getElementById('data-table');

            tableElement.innerHTML = '<div class="loading">Loading ' + dataType + 's...</div>';

            try {
                const response = await fetch('/api/' + dataType + 's');
                const result = await response.json();

                if (result.code === 0 && result.data) {
                    // Get sync status for this data type
                    const status = syncStatusData[dataType] || { sync_time: 'Never' };
                    const recordCount = result.data.length;
                    const syncTime = status.sync_time === 'Never' ? 'Never' : status.sync_time;

                    if (dataType === 'project') {
                        // For projects, create title with filters on the right
                        titleElement.innerHTML = `
                            <span>${dataType.charAt(0).toUpperCase() + dataType.slice(1)}s (${recordCount}) Last sync: ${syncTime}
                            </span>
                            <div class="data-title-operator">
                                ${generateTitleFilterOptions(result.data)}
                            </div>
                        `;
                    } else {
                        titleElement.textContent = `${dataType.charAt(0).toUpperCase() + dataType.slice(1)}s (${recordCount}) Last sync: ${syncTime}`;
                    }
                    displayDataTable(result.data, dataType);

                    // Set filter values after rendering for project type
                    if (dataType === 'project') {
                        setTitleFilterValues();
                    }
                } else {
                    if (dataType === 'project') {
                        titleElement.innerHTML = `
                            <span>${dataType.charAt(0).toUpperCase() + dataType.slice(1)}s (0) Last sync: Never</span>
                            <div class="data-title-operator">
                                ${generateTitleFilterOptions([])}
                            </div>
                        `;
                        setTitleFilterValues();
                    } else {
                        titleElement.textContent = `${dataType.charAt(0).toUpperCase() + dataType.slice(1)}s (0) Last sync: Never`;
                    }
                    tableElement.innerHTML = '<div class="error">Failed to load ' + dataType + 's</div>';
                }
            } catch (error) {
                if (dataType === 'project') {
                    titleElement.innerHTML = `
                        <span>${dataType.charAt(0).toUpperCase() + dataType.slice(1)}s (0) Last sync: Never</span>
                        <div class="data-title-operator">
                            ${generateTitleFilterOptions([])}
                        </div>
                    `;
                    setTitleFilterValues();
                } else {
                    titleElement.textContent = `${dataType.charAt(0).toUpperCase() + dataType.slice(1)}s (0) Last sync: Never`;
                }
                tableElement.innerHTML = '<div class="error">Error loading ' + dataType + 's: ' + error.message + '</div>';
            }
        }

        function displayDataTable(data, dataType, shouldSort = true) {
            const tableElement = document.getElementById('data-table');

            if (!data || data.length === 0) {
                tableElement.innerHTML = '<p>No ' + dataType + 's found.</p>';
                return;
            }

            // Store data for sorting and filtering
            if (shouldSort) {
                originalTableData = [...data];
            }
            currentTableData = data;

            // Sort customers by group by default (only on initial load)
            if (dataType === 'customer' && shouldSort && !sortConfig.column) {
                data.sort((a, b) => (a.group_name || '').localeCompare(b.group_name || ''));
            }

            let html = '<table class="data-table"><thead><tr>';

            // Generate table headers based on data type with sorting functionality
            if (dataType === 'product') {
                html += generateSortableHeader('id', 'ID', dataType);
                html += generateSortableHeader('name', 'Name', dataType);
                html += generateSortableHeader('description', 'Description', dataType);
            } else if (dataType === 'user') {
                html += generateSortableHeader('id', 'ID', dataType);
                html += generateSortableHeader('account', 'Account', dataType);
                html += generateSortableHeader('name', 'Name', dataType);
                html += generateSortableHeader('dept_name', 'Department', dataType);
                html += generateSortableHeader('role_name', 'Role', dataType);
                html += generateSortableHeader('enable', 'Status', dataType);
            } else if (dataType === 'project') {
                html += generateSortableHeader('id', 'ID', dataType);
                html += generateSortableHeader('code', 'Code', dataType);
                html += generateSortableHeader('name', 'Name', dataType);
                html += generateSortableHeader('products', 'Products', dataType);
                html += generateSortableHeader('customer', 'Customer', dataType);
                html += generateSortableHeader('comment', 'Comment', dataType);
            } else if (dataType === 'customer') {
                html += generateSortableHeader('id', 'ID', dataType);
                html += generateSortableHeader('unit_name', 'Unit Name', dataType);
                html += generateSortableHeader('group', 'Group', dataType);
            }

            html += '</tr></thead><tbody>';

            // Generate table rows
            data.forEach(item => {
                html += '<tr>';
                if (dataType === 'product') {
                    html += `<td>${item.id || ''}</td>`;
                    html += `<td>${item.name || ''}</td>`;
                    html += `<td>${item.description || ''}</td>`;
                } else if (dataType === 'user') {
                    html += `<td>${item.id || ''}</td>`;
                    html += `<td>${item.account || ''}</td>`;
                    html += `<td>${item.name || ''}</td>`;
                    html += `<td>${item.dept_name || ''}</td>`;
                    html += `<td>${item.role_name || ''}</td>`;
                    html += `<td><span class="status-badge ${item.enable ? 'status-active' : 'status-inactive'}">${item.enable ? 'Active' : 'Inactive'}</span></td>`;
                } else if (dataType === 'project') {
                    html += `<td>${item.id || ''}</td>`;
                    html += `<td>${item.project_code || ''}</td>`;
                    // Combine name and short name with / separator
                    const projectName = item.project_name || '';
                    const shortName = item.short_name || '';
                    let displayName = projectName;
                    if (shortName && shortName !== projectName) {
                        displayName = `${projectName} / ${shortName}`;
                    }
                    html += `<td>${displayName}</td>`;

                    // Display products list with names and links
                    let productsList = '';
                    if (item.rel_product_list) {
                        try {
                            const products = typeof item.rel_product_list === 'string'
                                ? JSON.parse(item.rel_product_list)
                                : item.rel_product_list;
                            if (Array.isArray(products) && products.length > 0) {
                                const productLinks = products.map(productId => {
                                    const product = getProductById(productId);
                                    if (product) {
                                        return `<a href="#" onclick="showProductDetails(${productId}); return false;" class="product-link">${product.name}</a>`;
                                    } else {
                                        return `ID:${productId}`;
                                    }
                                });
                                productsList = productLinks.join(', ');
                            }
                        } catch (e) {
                            // Handle string format like "1,2,3"
                            if (typeof item.rel_product_list === 'string') {
                                const productIds = item.rel_product_list.split(',').map(id => id.trim()).filter(id => id);
                                const productLinks = productIds.map(productId => {
                                    const product = getProductById(productId);
                                    if (product) {
                                        return `<a href="#" onclick="showProductDetails(${productId}); return false;" class="product-link">${product.name}</a>`;
                                    } else {
                                        return `ID:${productId}`;
                                    }
                                });
                                productsList = productLinks.join(', ');
                            } else {
                                productsList = item.rel_product_list;
                            }
                        }
                    }
                    html += `<td>${productsList}</td>`;

                    // Display customer with name and link
                    let customerDisplay = '';
                    if (item.rel_customer_info) {
                        const customer = getCustomerById(item.rel_customer_info);
                        if (customer) {
                            customerDisplay = `<a href="#" onclick="showCustomerDetails(${item.rel_customer_info}); return false;" class="customer-link">${customer.unit_name}</a>`;
                        } else {
                            customerDisplay = `ID:${item.rel_customer_info}`;
                        }
                    }
                    html += `<td>${customerDisplay}</td>`;

                    // Display comment
                    html += `<td>${item.project_comment || ''}</td>`;
                } else if (dataType === 'customer') {
                    html += `<td>${item.id || ''}</td>`;
                    html += `<td>${item.unit_name || ''}</td>`;
                    html += `<td>${item.group_name || ''}</td>`;
                }
                html += '</tr>';
            });

            html += '</tbody></table>';
            tableElement.innerHTML = html;
        }

        // Prevent multiple simultaneous calls to loadSyncStatus
        let isLoadingSyncStatus = false;

        async function loadSyncStatus() {
            // Prevent concurrent calls
            if (isLoadingSyncStatus) {
                console.debug('loadSyncStatus already in progress, skipping...');
                return;
            }

            isLoadingSyncStatus = true;
            try {
                const result = await fetchData('/api/sync/status');

                if (result.code === 0 && result.data) {
                    const statusByType = {};
                    result.data.forEach(status => {
                        if (!statusByType[status.data_type] ||
                            new Date(status.sync_time) > new Date(statusByType[status.data_type].sync_time)) {
                            statusByType[status.data_type] = status;
                        }
                    });

                    // Store sync status data for use in table titles
                    syncStatusData = statusByType;

                    // Auto-select statistics if no selection exists (only on first load)
                    if (selectedDataType === null) {
                        const statisticsTab = document.getElementById('tab-statistics');
                        if (statisticsTab) {
                            selectTab('statistics', statisticsTab);
                        }
                    }
                } else {
                    console.error('Failed to load sync status');
                }
            } catch (error) {
                console.error('Error loading sync status:', error);
            } finally {
                isLoadingSyncStatus = false;
            }
        }

        async function loadStatistics() {
            const titleElement = document.getElementById('data-title');
            const tableElement = document.getElementById('data-table');

            titleElement.innerHTML = `
                <span>Work Hour Reports</span>
                <div class="data-title-operator">
                    <button id="prevWeekBtn" class="btn" onclick="changeWeek(-1)">Prev Week</button>
                    <button id="thisWeekBtn" class="btn btn-primary period-btn active" data-period="week" onclick="selectPeriod('week', this)">This Week</button>
                    <button id="nextWeekBtn" class="btn" onclick="changeWeek(1)">Next Week</button>
                    <button class="btn period-btn" data-period="month" onclick="selectPeriod('month', this)">This Month</button>
                </div>
            `;

            try {
                const controlsHtml = `
                    <div id="stats_content">
                        <div class="loading">Loading statistics...</div>
                    </div>
                `;

                tableElement.innerHTML = controlsHtml;

                // Initialize week buttons
                updateWeekButtons();

                // Load default statistics (this week)
                loadStatisticsData();
            } catch (error) {
                tableElement.innerHTML = '<div class="error">Error loading statistics: ' + error.message + '</div>';
            }
        }

        let currentPeriod = 'week';
        let currentWeekOffset = 0; // 0 = this week, -1 = last week, 1 = next week

        function selectPeriod(period, buttonElement) {
            // Update active button
            document.querySelectorAll('.period-btn').forEach(btn => btn.classList.remove('active'));
            buttonElement.classList.add('active');

            currentPeriod = period;

            // Reset week offset when switching to week view
            if (period === 'week') {
                currentWeekOffset = 0;
                updateWeekButtons();
            }

            // Auto refresh when period changes
            loadStatisticsData();
        }

        function changeWeek(offset) {
            currentWeekOffset += offset;
            updateWeekButtons();
            loadStatisticsData();
        }

        function updateWeekButtons() {
            const prevBtn = document.getElementById('prevWeekBtn');
            const nextBtn = document.getElementById('nextWeekBtn');
            const thisWeekBtn = document.getElementById('thisWeekBtn');

            if (prevBtn && nextBtn && thisWeekBtn) {
                // Update button text and state
                if (currentWeekOffset === 0) {
                    thisWeekBtn.textContent = 'This Week';
                    prevBtn.disabled = false;
                    prevBtn.style.opacity = '1';
                    nextBtn.disabled = true;
                    nextBtn.style.opacity = '0.5';
                } else if (currentWeekOffset === -1) {
                    thisWeekBtn.textContent = 'Last Week';
                    prevBtn.disabled = false;
                    prevBtn.style.opacity = '1';
                    nextBtn.disabled = false;
                    nextBtn.style.opacity = '1';
                } else if (currentWeekOffset === 1) {
                    thisWeekBtn.textContent = 'Next Week';
                    prevBtn.disabled = false;
                    prevBtn.style.opacity = '1';
                    nextBtn.disabled = false;
                    nextBtn.style.opacity = '1';
                } else if (currentWeekOffset < -1) {
                    thisWeekBtn.textContent = `${Math.abs(currentWeekOffset)} Weeks Ago`;
                    prevBtn.disabled = false;
                    prevBtn.style.opacity = '1';
                    nextBtn.disabled = false;
                    nextBtn.style.opacity = '1';
                } else {
                    thisWeekBtn.textContent = `${currentWeekOffset} Weeks Later`;
                    prevBtn.disabled = false;
                    prevBtn.style.opacity = '1';
                    nextBtn.disabled = false;
                    nextBtn.style.opacity = '1';
                }
            }
        }

        async function loadStatisticsData() {
            const statsContent = document.getElementById('stats_content');

            let startDate, endDate;

            if (currentPeriod === 'week') {
                const weekRange = getCurrentWeekRange();
                startDate = weekRange.startDate;
                endDate = weekRange.endDate;
            } else if (currentPeriod === 'month') {
                const monthRange = getCurrentMonthRange();
                startDate = monthRange.start;
                endDate = monthRange.end;
            }

            statsContent.innerHTML = '<div class="loading">Loading statistics...</div>';

            try {
                // Use local work_hours table instead of remote data
                const response = await fetch(`/api/statistics/workhours?start_date=${startDate}&end_date=${endDate}`);
                const result = await response.json();

                if (result.code === 0 && result.data) {
                    await displayStatistics(result.data, currentPeriod, startDate, endDate);
                } else {
                    statsContent.innerHTML = '<div class="error">Failed to load statistics: ' + (result.message || 'Unknown error') + '</div>';
                }
            } catch (error) {
                statsContent.innerHTML = '<div class="error">Error loading statistics: ' + error.message + '</div>';
            }
        }

        async function displayStatistics(data, period, startDate, endDate) {
            const statsContent = document.getElementById('stats_content');

            if (period === 'week') {
                await displayWeeklyStatistics(data, startDate, endDate);
            } else {
                displayGeneralStatistics(data, startDate, endDate);
            }
        }

        async function getWorkHoursByUserAndDate(startDate, endDate) {
            try {
                const response = await fetch(`/api/workhours/range?start_date=${startDate}&end_date=${endDate}`);
                const result = await response.json();

                const workHoursByUserAndDate = {};

                if (result.code === 0 && result.data) {
                    result.data.forEach(wh => {
                        if (!workHoursByUserAndDate[wh.user_name]) {
                            workHoursByUserAndDate[wh.user_name] = {};
                        }
                        if (!workHoursByUserAndDate[wh.user_name][wh.work_date]) {
                            workHoursByUserAndDate[wh.user_name][wh.work_date] = [];
                        }
                        workHoursByUserAndDate[wh.user_name][wh.work_date].push(wh);
                    });
                }

                return workHoursByUserAndDate;
            } catch (error) {
                console.error('Error fetching work hours by date:', error);
                return {};
            }
        }

        function getWeekDates(startDate, endDate) {
            const dates = [];
            const start = new Date(startDate + 'T00:00:00.000Z');

            // Generate exactly 7 days starting from Monday in UTC+8
            for (let i = 0; i < 7; i++) {
                const date = new Date(start);
                date.setUTCDate(start.getUTCDate() + i);
                dates.push(date);
            }

            return dates;
        }

        function getDayName(dayIndex) {
            const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            return days[dayIndex];
        }

        // Get holiday data from API
        async function getHolidayData() {
            try {
                const response = await fetch('/api/holidays');
                const result = await response.json();
                if (result.code === 0 && result.data) {
                    return result.data;
                }
            } catch (error) {
                console.error('Failed to load holiday data:', error);
            }
            return [];
        }

        // Get day information including holiday status
        function getDayInfo(date, holidayData) {
            const dateStr = formatDateForDisplay(date);
            const dayOfWeek = date.getDay(); // 0=Sunday, 6=Saturday
            const dayName = getDayName(dayOfWeek);

            // Check if date is in holiday data
            const holiday = holidayData.find(h => h.date === dateStr);

            if (holiday && holiday.is_off_day) {
                // Holiday off day - replace weekday with holiday name
                return {
                    displayName: holiday.name,
                    isOffDay: true,
                    isWeekendWorkDay: false
                };
            } else if (dayOfWeek === 0 || dayOfWeek === 6) {
                // Weekend
                if (holiday && !holiday.is_off_day) {
                    // Weekend work day - show weekday name in red
                    return {
                        displayName: dayName,
                        isOffDay: false,
                        isWeekendWorkDay: true
                    };
                } else {
                    // Regular weekend - show as off day
                    return {
                        displayName: dayName,
                        isOffDay: true,
                        isWeekendWorkDay: false
                    };
                }
            } else {
                // Regular work day
                return {
                    displayName: dayName,
                    isOffDay: false,
                    isWeekendWorkDay: false
                };
            }
        }



        async function displayWeeklyStatistics(data, startDate, endDate) {
            const statsContent = document.getElementById('stats_content');

            // Get week dates (Monday to Sunday - exactly 7 days)
            const weekDates = getWeekDates(startDate, endDate);

            // Get all work hours for the period to organize by user and date
            const workHoursByUserAndDate = await getWorkHoursByUserAndDate(startDate, endDate);

            // Get holiday data for the period
            const holidayData = await getHolidayData();

            // Check which columns are empty (no data for any user)
            const emptyColumns = new Set();
            weekDates.forEach((date, index) => {
                const dateStr = formatDateForDisplay(date);
                let hasData = false;

                // Check if any user has data for this date
                for (const userName in workHoursByUserAndDate) {
                    if (workHoursByUserAndDate[userName] && workHoursByUserAndDate[userName][dateStr] && workHoursByUserAndDate[userName][dateStr].length > 0) {
                        hasData = true;
                        break;
                    }
                }

                if (!hasData) {
                    emptyColumns.add(index);
                }
            });

            // Calculate column widths
            const totalColumns = weekDates.length + 2; // +2 for User and Total columns
            const fixedWidth = 80 + 60; // User(80px) + Total(60px)
            let holidayColumns = 0;
            let workDayColumns = 0;

            weekDates.forEach((date) => {
                const dayInfo = getDayInfo(date, holidayData);
                if (dayInfo.isOffDay) {
                    holidayColumns++;
                } else {
                    workDayColumns++;
                }
            });

            const holidayWidth = 100; // 100px for each holiday column
            // All work day columns get equal width distribution
            const remainingWidth = workDayColumns > 0 ? `calc((100% - ${fixedWidth + (holidayColumns * holidayWidth)}px) / ${workDayColumns})` : '100px';

            // Build table header
            let tableHtml = `
                <table class="data-table weekly-stats-table">
                    <thead>
                        <tr>
                            <th style="width: 80px;">User</th>
            `;

            // Add date columns (Monday to Sunday)
            weekDates.forEach((date, index) => {
                const dateStr = formatDateForDisplay(date);
                const dayInfo = getDayInfo(date, holidayData);
                const emptyClass = emptyColumns.has(index) ? ' empty-column' : '';
                const holidayClass = dayInfo.isOffDay ? ' holiday-column' : '';
                const weekendWorkClass = dayInfo.isWeekendWorkDay ? ' weekend-work-column' : '';

                // Set width: holiday columns get fixed 100px, all work day columns get equal width
                const columnWidth = dayInfo.isOffDay ? '100px' : remainingWidth;

                tableHtml += `<th class="${emptyClass}${holidayClass}${weekendWorkClass}" style="width: ${columnWidth};">${dateStr}<br><small>(${dayInfo.displayName})</small></th>`;
            });

            tableHtml += `<th style="width: 60px;">Total</th></tr></thead><tbody>`;

            // Sort users to put "郭轩" first
            const sortedUsers = [...usersData].sort((a, b) => {
                if (a.name === "郭轩") return -1;
                if (b.name === "郭轩") return 1;
                return a.name.localeCompare(b.name);
            });

            // Show all users, not just those with work hours in the period
            sortedUsers.forEach(user => {
                tableHtml += `<tr><td style="width: 80px;">${user.name}</td>`;

                let totalHours = 0;

                // Add cells for each day
                weekDates.forEach((date, index) => {
                    const dateStr = formatDateForDisplay(date);
                    const userWorkHours = workHoursByUserAndDate[user.name] && workHoursByUserAndDate[user.name][dateStr];
                    const dayInfo = getDayInfo(date, holidayData);
                    const emptyClass = emptyColumns.has(index) ? ' empty-column' : '';
                    const holidayClass = dayInfo.isOffDay ? ' holiday-column' : '';

                    // Set width: holiday columns get fixed 100px, all work day columns get equal width
                    const columnWidth = dayInfo.isOffDay ? '100px' : remainingWidth;

                    if (userWorkHours && userWorkHours.length > 0) {
                        let dayHours = 0;
                        let dayDetails = userWorkHours.map(wh => {
                            dayHours += wh.duration_hours;
                            const project = getProjectById(wh.project_id);
                            const projectName = project ? project.project_name : `Project ${wh.project_id}`;

                            // Adjust project name length based on column type
                            const maxProjectNameLength = emptyColumns.has(index) ? 8 : 15; // Shorter for empty columns
                            const truncatedProjectName = projectName.length > maxProjectNameLength
                                ? projectName.substring(0, maxProjectNameLength) + '...'
                                : projectName;

                            return `
                                <div >
                                    <div class="project-detail">
                                        <span class="project-name">${truncatedProjectName}</span>
                                        <span class="work-hour">${wh.duration_hours}h</span>
                                    </div>
                                    ${wh.work_description ? `<div >${wh.work_description}</div>` : ''}
                                </div>
                            `;
                        }).join('');

                        totalHours += dayHours;

                        // Special handling for "郭轩" - add edit icon when has data
                        const editIcon = user.name === "郭轩" ? `
                            <button class="work-hour-edit-btn" onclick="openWorkHourPanel('edit', '${user.name}', '${dateStr}')" title="Edit work hours">
                                <svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                </svg>
                            </button>
                        ` : '';

                        tableHtml += `
                            <td class="${emptyClass}${holidayClass}" style="width: ${columnWidth};">
                                <div class="work-hour-cell">
                                    <div class="work-hour-content">
                                        ${dayDetails}
                                    </div>
                                    ${editIcon}
                                </div>
                            </td>
                        `;
                    } else {
                        // Special handling for "郭轩" - add create button when no data
                        const createButton = user.name === "郭轩" ? `
                            <button class="work-hour-create-btn" onclick="openWorkHourPanel('create', '${user.name}', '${dateStr}')">Create</button>
                        ` : '-';

                        tableHtml += `<td class="${emptyClass}${holidayClass}" style="width: ${columnWidth};">${createButton}</td>`;
                    }
                });

                tableHtml += `<td style="width: 60px;">${totalHours}h</td></tr>`;
            });

            tableHtml += '</tbody></table>';
            statsContent.innerHTML = tableHtml;
        }

        function displayGeneralStatistics(data, startDate, endDate) {
            const statsContent = document.getElementById('stats_content');

            if (!data.by_user || data.by_user.length === 0) {
                statsContent.innerHTML = '<div class="error">No work hour data found for the selected period.</div>';
                return;
            }

            let tableHtml = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>User Name</th>
                            <th>Total Hours</th>
                            <th>Records</th>
                            <th>Work Days</th>
                            <th>Avg Hours/Day</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Sort users to put "郭轩" first
            const sortedUsers = [...data.by_user].sort((a, b) => {
                if (a.user_name === "郭轩") return -1;
                if (b.user_name === "郭轩") return 1;
                return a.user_name.localeCompare(b.user_name);
            });

            sortedUsers.forEach(user => {
                const avgHours = user.work_days > 0 ? (user.total_hours / user.work_days).toFixed(1) : '0.0';

                tableHtml += `
                    <tr>
                        <td >${user.user_name}</td>
                        <td >${user.total_hours}h</td>
                        <td>${user.record_count}</td>
                        <td>${user.work_days}</td>
                        <td>${avgHours}h</td>
                    </tr>
                `;
            });

            tableHtml += '</tbody></table>';

            // Add project statistics if available
            if (data.by_project && data.by_project.length > 0) {
                tableHtml += `
                    <h3 >Hours by Project</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Project</th>
                                <th>Total Hours</th>
                                <th>Records</th>
                                <th>Contributors</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                data.by_project.forEach(project => {
                    const projectInfo = getProjectById(project.project_id);
                    const projectName = projectInfo ? projectInfo.project_name : `Project ${project.project_id}`;

                    tableHtml += `
                        <tr>
                            <td >${projectName}</td>
                            <td >${project.total_hours}h</td>
                            <td>${project.record_count}</td>
                            <td>${project.user_count}</td>
                        </tr>
                    `;
                });

                tableHtml += '</tbody></table>';
            }

            statsContent.innerHTML = tableHtml;
        }

        function formatDateForDisplay(date) {
            // Convert to UTC+8 timezone for display
            const utc8Date = new Date(date.getTime() + (8 * 60 * 60 * 1000));
            const year = utc8Date.getUTCFullYear();
            const month = String(utc8Date.getUTCMonth() + 1).padStart(2, '0');
            const day = String(utc8Date.getUTCDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // Professional lunar calendar calculation using lunar-javascript library
        function getLunarDate(date) {
            try {
                // Use the professional lunar library
                const lunar = Lunar.fromDate(date);

                return {
                    month: lunar.getMonth().toString(),
                    day: lunar.getDay().toString(),
                };
            } catch (error) {
                console.warn('Error calculating lunar date:', error);
                return {
                    month: "",
                    day: "",
                };
            }
        }

        // Load initial data
        document.addEventListener('DOMContentLoaded', async function () {
            // Load reference data first
            await loadReferenceData();

            // Load sync status and auto-select first tab
            await loadSyncStatus();

            // Auto-select the first tab (Reports)
            const firstTab = document.getElementById('tab-statistics');
            if (firstTab) {
                selectTab('statistics', firstTab);
            }

            // Auto refresh sync status every 30 seconds (less frequent to reduce server load)
            setInterval(loadSyncStatus, 30 * 1000);
        });

        // Database tool functionality
        let dbCurrentTable = null;
        let dbAllTableData = null;
        let dbCurrentPage = 1;
        let dbPageSize = 100;
        let dbTotalPages = 1;
        let dbSortConfig = { column: null, direction: 'asc' };

        async function loadDatabaseTool() {
            // Update active tab
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.closest('.nav-tab').classList.add('active');

            // Set title and load database interface
            document.getElementById('data-title').textContent = 'Database Query Tool';

            const tableElement = document.getElementById('data-table');
            tableElement.innerHTML = `
                <div style="display: flex; flex-direction: column; height: 100%; min-height: 600px;">
                    <!-- Query Section -->
                    <div style="background: #fff; padding: 20px; border-bottom: 1px solid #eaeaea;">
                        <div style="display: flex; gap: 16px; align-items: flex-start;">
                            <textarea id="dbSqlInput" placeholder="Enter your SQL query here (SELECT statements only)..."
                                style="flex: 1; padding: 12px 16px; border: 1px solid #eaeaea; 
                                font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
                                font-size: 14px; resize: vertical; min-height: 100px; background-color: #fafafa; line-height: 1.5;"></textarea>
                            <button onclick="dbExecuteQuery()" style="padding: 12px 24px; background: #000; color: #fff;
                                border: 1px solid #000; cursor: pointer; font-weight: 500;
                                font-family: inherit; font-size: 14px;">Execute Query</button>
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div style="flex: 1; display: flex; min-height: 0;">
                        <!-- Sidebar -->
                        <div style="width: 280px; background: #fafafa; border-right: 1px solid #eaeaea; overflow: hidden;">
                            <div style="background: #fff; padding: 20px 24px; border-bottom: 1px solid #eaeaea;
                                font-weight: 600; color: #000; font-size: 14px;">Tables</div>
                            <div id="dbTableList" style="max-height: 400px; overflow-y: auto;">
                                <div style="text-align: center; padding: 60px 32px; color: #666; font-size: 14px;">Loading tables...</div>
                            </div>
                        </div>

                        <!-- Main Content -->
                        <div style="flex: 1; background: #fff; display: flex; flex-direction: column; overflow: hidden;">
                            <div id="dbContentHeader" style="background: #fff; padding: 20px 32px; border-bottom: 1px solid #eaeaea;
                                font-weight: 600; color: #000; font-size: 16px;">Select a table or execute a query</div>
                            <div id="dbTableContainer" style="flex: 1; overflow: auto; padding: 32px;">
                                <div style="text-align: center; padding: 80px 32px; color: #666;">
                                    <h3 style="margin-bottom: 12px; color: #000; font-size: 18px; font-weight: 600;">Welcome to Database Query Tool</h3>
                                    <p style="font-size: 14px; line-height: 1.5;">Select a table from the left sidebar to view its data, or enter a SQL query above.</p>
                                </div>
                            </div>
                            <div id="dbPagination" style="display: none; justify-content: space-between; align-items: center;
                                padding: 20px 32px; background: #fff; border-top: 1px solid #eaeaea;">
                                <div id="dbPaginationInfo" style="font-size: 14px; color: #666;"></div>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <button id="dbFirstPageBtn" onclick="dbGoToPage(1)" style="padding: 8px 12px; background: #fff; color: #000;
                                        border: 1px solid #eaeaea; cursor: pointer; font-size: 14px;">First</button>
                                    <button id="dbPrevPageBtn" onclick="dbGoToPage(dbCurrentPage - 1)" style="padding: 8px 12px; background: #fff; color: #000;
                                        border: 1px solid #eaeaea; cursor: pointer; font-size: 14px;">Previous</button>
                                    <span>Page</span>
                                    <input type="number" id="dbPageInput" min="1" onchange="dbGoToPageInput()" onkeypress="dbHandlePageInputKeypress(event)"
                                        style="width: 60px; padding: 6px 8px; border: 1px solid #eaeaea; text-align: center; font-size: 14px;">
                                    <span>of <span id="dbTotalPages">1</span></span>
                                    <button id="dbNextPageBtn" onclick="dbGoToPage(dbCurrentPage + 1)" style="padding: 8px 12px; background: #fff; color: #000;
                                        border: 1px solid #eaeaea; cursor: pointer; font-size: 14px;">Next</button>
                                    <button id="dbLastPageBtn" onclick="dbGoToPage(dbTotalPages)" style="padding: 8px 12px; background: #fff; color: #000;
                                        border: 1px solid #eaeaea; cursor: pointer; font-size: 14px;">Last</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Always load tables when switching to database tool
            // This ensures fresh data and handles cases where the page was refreshed
            await dbLoadTables();

            // Add CSS for database tool if not already added
            if (!document.getElementById('db-tool-styles')) {
                const style = document.createElement('style');
                style.id = 'db-tool-styles';
                style.textContent = `
                    .db-data-table {
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 14px;
                        border: 1px solid #eaeaea;
                        overflow: hidden;
                    }
                    .db-data-table th,
                    .db-data-table td {
                        padding: 12px 16px;
                        text-align: left;
                        border-bottom: 1px solid #eaeaea;
                    }
                    .db-data-table th {
                        background-color: #fafafa;
                        font-weight: 600;
                        position: sticky;
                        top: 0;
                        z-index: 1;
                        color: #000;
                        cursor: pointer;
                        user-select: none;
                        position: relative;
                    }
                    .db-data-table th:hover {
                        background-color: #f0f0f0;
                    }
                    .db-sort-indicator {
                        position: absolute;
                        right: 8px;
                        top: 50%;
                        transform: translateY(-50%);
                        font-size: 12px;
                        color: #666;
                    }
                    .db-sort-indicator.asc::after {
                        content: '▲';
                    }
                    .db-sort-indicator.desc::after {
                        content: '▼';
                    }
                    .db-data-table tr:nth-child(even) {
                        background-color: #fafafa;
                    }
                    .db-data-table tr:hover {
                        background-color: #f5f5f5;
                    }
                    .db-data-table tr:last-child td {
                        border-bottom: none;
                    }
                    .db-table-item {
                        padding: 12px 24px;
                        cursor: pointer;
                        border-bottom: 1px solid #eaeaea;
                        font-size: 14px;
                        color: #666;
                    }
                    .db-table-item:hover {
                        background-color: #f5f5f5;
                        color: #000;
                    }
                    .db-table-item.active {
                        background-color: #000;
                        color: #fff;
                        font-weight: 500;
                    }
                    .db-error {
                        background-color: #ffeaea;
                        color: #e00;
                        padding: 16px 20px;
                        margin: 20px;
                        border: 1px solid #f5c6cb;
                        font-size: 14px;
                    }
                `;
                document.head.appendChild(style);
            }

            // Handle Ctrl+Enter in textarea
            document.getElementById('dbSqlInput').addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'Enter') {
                    dbExecuteQuery();
                }
            });
        }

        // Calendar tool functionality
        async function loadCalendarTool() {
            // Update active tab
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.closest('.nav-tab').classList.add('active');

            // Set title and load calendar interface
            document.getElementById('data-title').textContent = 'Annual Calendar';

            const tableElement = document.getElementById('data-table');

            // Get current year
            const currentYear = new Date().getFullYear();

            // Get holiday data
            const holidayData = await getHolidayData();

            // Generate calendar HTML
            const calendarHtml = generateAnnualCalendar(currentYear, holidayData);

            tableElement.innerHTML = calendarHtml;
        }

        function generateAnnualCalendar(year, holidayData) {
            const monthNames = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];

            const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

            let html = `
                <div class="calendar-grid">
            `;

            // Generate 12 month cards
            for (let month = 0; month < 12; month++) {
                html += generateMonthCard(year, month, dayNames, holidayData);
            }

            html += `
                </div>
            `;

            return html;
        }

        function generateMonthCard(year, month, dayNames, holidayData) {
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const daysInMonth = lastDay.getDate();

            // Get first day of week (0=Sunday, 1=Monday, etc.)
            // Convert to Monday=0, Sunday=6
            let firstDayOfWeek = firstDay.getDay();
            firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

            let html = `
                <div class="month-card">
                    <div class="month-header">
                        <h3>${year}-${String(month + 1).padStart(2, '0')}</h3>
                    </div>
                    <div class="month-calendar">
                        <div class="day-headers">
            `;

            // Add day headers
            dayNames.forEach(day => {
                html += `<div class="day-header">${day}</div>`;
            });

            html += `</div><div class="days-grid">`;

            // Add empty cells for days before month starts
            for (let i = 0; i < firstDayOfWeek; i++) {
                html += `<div class="day-cell empty"></div>`;
            }

            // Get today's date for comparison
            const today = new Date();
            const todayStr = formatDateForDisplay(today);

            // Add days of the month
            for (let day = 1; day <= daysInMonth; day++) {
                const date = new Date(year, month, day);
                const dateStr = formatDateForDisplay(date);
                const dayInfo = getDayInfo(date, holidayData);

                let dayClass = 'day-cell';
                let titleText = dayInfo.displayName;

                // Check if this is today
                if (dateStr === todayStr) {
                    dayClass += ' today';
                    titleText = `Today - ${dayInfo.displayName}`;
                }

                if (dayInfo.isOffDay) {
                    dayClass += ' holiday';
                    titleText = `Off Day - ${dayInfo.displayName}`;
                } else if (dayInfo.isWeekendWorkDay) {
                    dayClass += ' weekend-work';
                    titleText = `Work Day - ${dayInfo.displayName}`;
                }

                // Determine what to show below the day number
                let bottomText = '';
                if (dayInfo.isOffDay && dayInfo.displayName !== 'Sat' && dayInfo.displayName !== 'Sun') {
                    // Show holiday name for holidays
                    bottomText = `<span class="holiday-name">${dayInfo.displayName}</span>`;
                } else {
                    // Show lunar date for regular days
                    const lunarDate = getLunarDate(date);
                    bottomText = `<span class="lunar-date">${lunarDate.month}.${lunarDate.day}</span>`;
                }

                html += `
                    <div class="${dayClass}" title="${titleText}">
                        <span class="day-number">${day}</span>
                        ${bottomText}
                    </div>
                `;
            }

            html += `
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // Configuration tool functionality
        let configToolLoaded = false;
        let currentConfig = null;

        async function loadConfigTool() {
            // Update active tab
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.closest('.nav-tab').classList.add('active');

            const titleElement = document.getElementById('data-title');
            titleElement.innerHTML = `
                <span>System Configuration</span>
            `;

            const tableElement = document.getElementById('data-table');
            tableElement.innerHTML = `
                <div id="configContent" style="width: 100%; padding: 0 20px;">
                    <div style="text-align: center; padding: 60px 32px; color: #666; font-size: 14px;">Loading configuration...</div>
                </div>
            `;

            // Load current configuration
            await loadCurrentConfiguration();
        }

        async function loadCurrentConfiguration() {
            try {
                const response = await fetch('/api/config');
                const result = await response.json();

                if (result.code === 0) {
                    currentConfig = result.data;
                    displayConfigurationForm();
                } else {
                    showConfigError('Failed to load configuration: ' + result.message);
                }
            } catch (error) {
                showConfigError('Error loading configuration: ' + error.message);
            }
        }

        function displayConfigurationForm() {
            const container = document.getElementById('configContent');

            const html = `
                <div class="config-form">
                    <div class="config-grid">
                        <div class="config-card">
                            <div class="config-card-header">
                                <h4>Server Configuration</h4>
                            </div>
                            <div class="config-card-body">
                                <div class="config-row">
                                    <label>Host:</label>
                                    <input type="text" id="server_host" value="${currentConfig.server.host}" />
                                </div>
                                <div class="config-row">
                                    <label>Port:</label>
                                    <input type="number" id="server_port" value="${currentConfig.server.port}" min="1" max="65535" />
                                </div>
                                <div class="config-row">
                                    <label>Threads:</label>
                                    <input type="number" id="server_threads" value="${currentConfig.server.threads}" min="1" max="16" />
                                </div>
                            </div>
                        </div>

                        <div class="config-card">
                            <div class="config-card-header">
                                <h4>Database Configuration</h4>
                            </div>
                            <div class="config-card-body">
                                <div class="config-row">
                                    <label>Database Path:</label>
                                    <input type="text" id="database_path" value="${currentConfig.database.path}" />
                                </div>
                                <div class="config-row">
                                    <label>Pool Size:</label>
                                    <input type="number" id="database_pool_size" value="${currentConfig.database.connection_pool_size}" min="1" max="50" />
                                </div>
                                <div class="config-row">
                                    <label>WAL Mode:</label>
                                    <input type="checkbox" id="database_wal_mode" ${currentConfig.database.enable_wal_mode ? 'checked' : ''} />
                                </div>
                                <div class="config-row">
                                    <label>Timeout (ms):</label>
                                    <input type="number" id="database_busy_timeout" value="${currentConfig.database.busy_timeout}" min="1000" max="60000" />
                                </div>
                            </div>
                        </div>

                        <div class="config-card">
                            <div class="config-card-header">
                                <h4>Backend Configuration</h4>
                            </div>
                            <div class="config-card-body">
                                <div class="config-row">
                                    <label>Base URL:</label>
                                    <input type="url" id="backend_base_url" value="${currentConfig.backend.base_url}" />
                                </div>
                                <div class="config-row">
                                    <label>Timeout (sec):</label>
                                    <input type="number" id="backend_timeout" value="${currentConfig.backend.timeout}" min="5" max="300" />
                                </div>
                                <div class="config-row">
                                    <label>Retry Count:</label>
                                    <input type="number" id="backend_retry_count" value="${currentConfig.backend.retry_count}" min="0" max="10" />
                                </div>
                                <div class="config-row">
                                    <label>Auth Type:</label>
                                    <select id="backend_auth_type" onchange="toggleAuthFields()">
                                        <option value="none" ${currentConfig.backend.auth.type === 'none' ? 'selected' : ''}>None</option>
                                        <option value="header" ${currentConfig.backend.auth.type === 'header' ? 'selected' : ''}>Header</option>
                                        <option value="token" ${currentConfig.backend.auth.type === 'token' ? 'selected' : ''}>Token</option>
                                    </select>
                                </div>
                                <div class="config-row" id="auth_header_name_row" style="display: ${currentConfig.backend.auth.type === 'header' ? 'flex' : 'none'};">
                                    <label>Header Name:</label>
                                    <input type="text" id="backend_auth_header_name" value="${currentConfig.backend.auth.header_name}" />
                                </div>
                                <div class="config-row" id="auth_header_value_row" style="display: ${currentConfig.backend.auth.type === 'header' ? 'flex' : 'none'};">
                                    <label>Header Value:</label>
                                    <textarea id="backend_auth_header_value" rows="2">${currentConfig.backend.auth.header_value}</textarea>
                                </div>
                                <div class="config-row" id="auth_token_row" style="display: ${currentConfig.backend.auth.type === 'token' ? 'flex' : 'none'};">
                                    <label>Token:</label>
                                    <textarea id="backend_auth_token" rows="2">${currentConfig.backend.auth.token}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="config-card">
                            <div class="config-card-header">
                                <h4>Sync Configuration</h4>
                            </div>
                            <div class="config-card-body">
                                <div class="config-row">
                                    <label>Interval (min):</label>
                                    <input type="number" id="sync_interval_minutes" value="${currentConfig.sync.interval_minutes}" min="1" max="1440" />
                                </div>
                                <div class="config-row">
                                    <label>Auto Start:</label>
                                    <input type="checkbox" id="sync_auto_start" ${currentConfig.sync.auto_start ? 'checked' : ''} />
                                </div>
                            </div>
                        </div>

                        <div class="config-card">
                            <div class="config-card-header">
                                <h4>Holiday API Configuration</h4>
                            </div>
                            <div class="config-card-body">
                                <div class="config-row">
                                    <label>Data Source:</label>
                                    <select id="holiday_api_source">
                                        <option value="github" ${(!currentConfig.holiday_api || !currentConfig.holiday_api.sources || currentConfig.holiday_api.sources[0].includes('raw.githubusercontent.com')) ? 'selected' : ''}>GitHub Raw</option>
                                        <option value="jsdelivr" ${(currentConfig.holiday_api && currentConfig.holiday_api.sources && currentConfig.holiday_api.sources[0].includes('cdn.jsdelivr.net')) ? 'selected' : ''}>JSDelivr CDN</option>
                                    </select>
                                </div>
                                <div class="config-row">
                                    <label>Connect Timeout (s):</label>
                                    <input type="number" id="holiday_api_connection_timeout" value="${(currentConfig.holiday_api && currentConfig.holiday_api.timeout) ? currentConfig.holiday_api.timeout.connection : 20}" min="5" max="60" />
                                </div>
                                <div class="config-row">
                                    <label>Read Timeout (s):</label>
                                    <input type="number" id="holiday_api_read_timeout" value="${(currentConfig.holiday_api && currentConfig.holiday_api.timeout) ? currentConfig.holiday_api.timeout.read : 30}" min="5" max="120" />
                                </div>
                                <div class="config-row">
                                    <label>Write Timeout (s):</label>
                                    <input type="number" id="holiday_api_write_timeout" value="${(currentConfig.holiday_api && currentConfig.holiday_api.timeout) ? currentConfig.holiday_api.timeout.write : 10}" min="5" max="60" />
                                </div>
                                <div class="config-row">
                                    <label>Retry Count:</label>
                                    <input type="number" id="holiday_api_retry_count" value="${(currentConfig.holiday_api) ? currentConfig.holiday_api.retry_count : 3}" min="1" max="10" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 30px; display: flex; gap: 15px; justify-content: center;">
                        <button id="configSaveBtn" onclick="saveConfiguration()" style="padding: 12px 24px; background: #000; color: #fff; border: 1px solid #000; cursor: pointer; font-weight: 500; font-size: 14px;">
                            Save Configuration
                        </button>
                        <button onclick="resetConfiguration()" style="padding: 12px 24px; background: #fff; color: #666; border: 1px solid #eaeaea; cursor: pointer; font-weight: 500; font-size: 14px;">
                            Reset Changes
                        </button>
                    </div>

                    <div id="configMessage" style="margin-top: 20px;"></div>
                </div>
            `;

            container.innerHTML = html;

            // Add CSS for configuration form if not already added
            if (!document.getElementById('config-tool-styles')) {
                const style = document.createElement('style');
                style.id = 'config-tool-styles';
                style.textContent = `
                    .config-form {
                        width: 100%;
                        margin: 0;
                    }
                    .config-grid {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin-bottom: 20px;
                        width: 100%;
                    }
                    .config-card {
                        background: #fff;
                        border: 1px solid #eaeaea;
                        display: flex;
                        flex-direction: column;
                        min-height: 300px;
                        width: 100%;
                    }
                    .config-card-header {
                        background: #f8f9fa;
                        border-bottom: 1px solid #eaeaea;
                        padding: 16px 20px;
                    }
                    .config-card-header h4 {
                        margin: 0;
                        color: #000;
                        font-size: 16px;
                        font-weight: 600;
                    }
                    .config-card-body {
                        padding: 20px;
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: 16px;
                    }
                    .config-row {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                    }
                    .config-row label {
                        min-width: 140px;
                        font-weight: 500;
                        color: #333;
                        font-size: 14px;
                        flex-shrink: 0;
                    }
                    .config-row input, .config-row select, .config-row textarea {
                        flex: 1;
                        padding: 8px 12px;
                        border: 1px solid #eaeaea;
                        font-size: 14px;
                        font-family: inherit;
                    }
                    .config-row input[type="checkbox"] {
                        flex: none;
                        width: 18px;
                        height: 18px;
                    }
                    .config-row input:focus, .config-row select:focus, .config-row textarea:focus {
                        outline: none;
                        border-color: #000;
                        box-shadow: 0 0 0 2px rgba(0,0,0,0.1);
                    }
                    .config-error {
                        background-color: #ffeaea;
                        color: #e00;
                        padding: 16px 20px;
                        border: 1px solid #f5c6cb;
                        font-size: 14px;
                    }
                    .config-success {
                        background-color: #eaf4ea;
                        color: #2e7d32;
                        padding: 16px 20px;
                        border: 1px solid #c8e6c9;
                        font-size: 14px;
                    }
                    @media (max-width: 1200px) {
                        .config-grid {
                            grid-template-columns: repeat(2, 1fr);
                        }
                    }
                    @media (max-width: 768px) {
                        .config-grid {
                            grid-template-columns: 1fr;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        function toggleAuthFields() {
            const authType = document.getElementById('backend_auth_type').value;
            const headerNameRow = document.getElementById('auth_header_name_row');
            const headerValueRow = document.getElementById('auth_header_value_row');
            const tokenRow = document.getElementById('auth_token_row');

            // Hide all auth fields first
            headerNameRow.style.display = 'none';
            headerValueRow.style.display = 'none';
            tokenRow.style.display = 'none';

            // Show relevant fields based on auth type
            if (authType === 'header') {
                headerNameRow.style.display = 'flex';
                headerValueRow.style.display = 'flex';
            } else if (authType === 'token') {
                tokenRow.style.display = 'flex';
            }
        }

        function getHolidayApiSources() {
            const selectedSource = document.getElementById('holiday_api_source').value;

            switch (selectedSource) {
                case 'github':
                    return ['https://raw.githubusercontent.com/NateScarlet/holiday-cn/master/{year}.json'];
                case 'jsdelivr':
                    return ['https://cdn.jsdelivr.net/gh/NateScarlet/holiday-cn@master/{year}.json'];
                default:
                    return ['https://raw.githubusercontent.com/NateScarlet/holiday-cn/master/{year}.json'];
            }
        }

        async function saveConfiguration() {
            const saveBtn = document.getElementById('configSaveBtn');
            const originalText = saveBtn.textContent;

            try {
                saveBtn.disabled = true;
                saveBtn.textContent = 'Saving...';

                // Collect configuration data from form
                const newConfig = {
                    server: {
                        host: document.getElementById('server_host').value,
                        port: parseInt(document.getElementById('server_port').value),
                        threads: parseInt(document.getElementById('server_threads').value)
                    },
                    database: {
                        path: document.getElementById('database_path').value,
                        connection_pool_size: parseInt(document.getElementById('database_pool_size').value),
                        enable_wal_mode: document.getElementById('database_wal_mode').checked,
                        busy_timeout: parseInt(document.getElementById('database_busy_timeout').value)
                    },
                    backend: {
                        base_url: document.getElementById('backend_base_url').value,
                        timeout: parseInt(document.getElementById('backend_timeout').value),
                        retry_count: parseInt(document.getElementById('backend_retry_count').value),
                        auth: {
                            type: document.getElementById('backend_auth_type').value,
                            header_name: document.getElementById('backend_auth_header_name').value,
                            header_value: document.getElementById('backend_auth_header_value').value,
                            token: document.getElementById('backend_auth_token').value
                        }
                    },
                    sync: {
                        interval_minutes: parseInt(document.getElementById('sync_interval_minutes').value),
                        auto_start: document.getElementById('sync_auto_start').checked
                    },
                    holiday_api: {
                        sources: getHolidayApiSources(),
                        timeout: {
                            connection: parseInt(document.getElementById('holiday_api_connection_timeout').value),
                            read: parseInt(document.getElementById('holiday_api_read_timeout').value),
                            write: parseInt(document.getElementById('holiday_api_write_timeout').value)
                        },
                        retry_count: parseInt(document.getElementById('holiday_api_retry_count').value),
                        user_agent: "DailyReport-HolidaySync/1.0"
                    }
                };

                // Validate configuration
                if (!validateConfiguration(newConfig)) {
                    return;
                }

                // Save configuration
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(newConfig)
                });

                const result = await response.json();

                if (result.code === 0) {
                    currentConfig = newConfig;
                    showConfigSuccess('Configuration saved successfully!');

                    // Show restart dialog
                    showRestartDialog();
                } else {
                    showConfigError('Failed to save configuration: ' + result.message);
                }
            } catch (error) {
                showConfigError('Error saving configuration: ' + error.message);
            } finally {
                saveBtn.disabled = false;
                saveBtn.textContent = originalText;
            }
        }

        function validateConfiguration(config) {
            const errors = [];

            // Validate server configuration
            if (!config.server.host || config.server.host.trim() === '') {
                errors.push('Server host is required');
            }
            if (config.server.port < 1 || config.server.port > 65535) {
                errors.push('Server port must be between 1 and 65535');
            }
            if (config.server.threads < 1 || config.server.threads > 16) {
                errors.push('Server threads must be between 1 and 16');
            }

            // Validate database configuration
            if (!config.database.path || config.database.path.trim() === '') {
                errors.push('Database path is required');
            }
            if (config.database.connection_pool_size < 1 || config.database.connection_pool_size > 50) {
                errors.push('Database connection pool size must be between 1 and 50');
            }
            if (config.database.busy_timeout < 1000 || config.database.busy_timeout > 60000) {
                errors.push('Database busy timeout must be between 1000 and 60000 ms');
            }

            // Validate backend configuration
            if (!config.backend.base_url || config.backend.base_url.trim() === '') {
                errors.push('Backend base URL is required');
            }
            if (config.backend.timeout < 5 || config.backend.timeout > 300) {
                errors.push('Backend timeout must be between 5 and 300 seconds');
            }
            if (config.backend.retry_count < 0 || config.backend.retry_count > 10) {
                errors.push('Backend retry count must be between 0 and 10');
            }

            // Validate auth configuration
            if (config.backend.auth.type === 'header') {
                if (!config.backend.auth.header_name || config.backend.auth.header_name.trim() === '') {
                    errors.push('Auth header name is required when using header authentication');
                }
                if (!config.backend.auth.header_value || config.backend.auth.header_value.trim() === '') {
                    errors.push('Auth header value is required when using header authentication');
                }
            } else if (config.backend.auth.type === 'token') {
                if (!config.backend.auth.token || config.backend.auth.token.trim() === '') {
                    errors.push('Auth token is required when using token authentication');
                }
            }

            // Validate sync configuration
            if (config.sync.interval_minutes < 1 || config.sync.interval_minutes > 1440) {
                errors.push('Sync interval must be between 1 and 1440 minutes');
            }

            // Validate holiday API configuration
            if (config.holiday_api) {
                if (!config.holiday_api.sources || config.holiday_api.sources.length === 0) {
                    errors.push('Holiday API source is required');
                }

                if (config.holiday_api.timeout) {
                    if (config.holiday_api.timeout.connection < 5 || config.holiday_api.timeout.connection > 60) {
                        errors.push('Holiday API connection timeout must be between 5 and 60 seconds');
                    }
                    if (config.holiday_api.timeout.read < 5 || config.holiday_api.timeout.read > 120) {
                        errors.push('Holiday API read timeout must be between 5 and 120 seconds');
                    }
                    if (config.holiday_api.timeout.write < 5 || config.holiday_api.timeout.write > 60) {
                        errors.push('Holiday API write timeout must be between 5 and 60 seconds');
                    }
                }

                if (config.holiday_api.retry_count < 1 || config.holiday_api.retry_count > 10) {
                    errors.push('Holiday API retry count must be between 1 and 10');
                }
            }

            if (errors.length > 0) {
                showConfigError('Validation errors:\n' + errors.join('\n'));
                return false;
            }

            return true;
        }

        function resetConfiguration() {
            if (currentConfig && confirm('Are you sure you want to reset all changes? This will reload the current configuration from the server.')) {
                displayConfigurationForm();
                showConfigSuccess('Configuration reset to current saved values.');
            }
        }

        function showConfigError(message) {
            const messageDiv = document.getElementById('configMessage');
            messageDiv.innerHTML = `<div class="config-error">${message.replace(/\n/g, '<br>')}</div>`;
        }

        function showConfigSuccess(message) {
            const messageDiv = document.getElementById('configMessage');
            messageDiv.innerHTML = `<div class="config-success">${message}</div>`;
        }

        function showRestartDialog() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="background: #fff; padding: 30px; max-width: 500px; margin: 20px;">
                    <h3 style="margin: 0 0 20px 0; color: #000; font-size: 18px; font-weight: 600;">Restart Required</h3>
                    <p style="margin: 0 0 20px 0; color: #666; line-height: 1.5;">
                        Configuration has been saved successfully. The application needs to be restarted for changes to take effect.
                    </p>
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="closeRestartDialog()" style="padding: 10px 20px; background: #fff; color: #666;
                            border: 1px solid #eaeaea; cursor: pointer; font-weight: 500;">
                            Restart Later
                        </button>
                        <button onclick="restartNow()" style="padding: 10px 20px; background: #000; color: #fff;
                            border: 1px solid #000; cursor: pointer; font-weight: 500;">
                            Restart Now
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentRestartModal = modal;
        }

        function closeRestartDialog() {
            if (window.currentRestartModal) {
                document.body.removeChild(window.currentRestartModal);
                window.currentRestartModal = null;
            }
        }

        async function restartNow() {
            try {
                closeRestartDialog();
                showConfigSuccess('Restarting application...');

                const response = await fetch('/api/restart', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.code === 0) {
                    // Show countdown and reload page
                    let countdown = 5;
                    const countdownInterval = setInterval(() => {
                        showConfigSuccess(`Application is restarting... Page will reload in ${countdown} seconds.`);
                        countdown--;

                        if (countdown < 0) {
                            clearInterval(countdownInterval);
                            window.location.reload();
                        }
                    }, 1000);
                } else {
                    showConfigError('Failed to restart application: ' + result.message);
                }
            } catch (error) {
                showConfigError('Error restarting application: ' + error.message);
            }
        }



        // Database tool functions
        async function dbLoadTables() {
            try {
                const response = await fetch('/api/db/tables');
                const result = await response.json();

                if (result.code === 0) {
                    dbDisplayTables(result.data);
                } else {
                    dbShowError('Failed to load tables: ' + result.message);
                }
            } catch (error) {
                dbShowError('Error loading tables: ' + error.message);
            }
        }

        function dbDisplayTables(tables) {
            const tableList = document.getElementById('dbTableList');
            tableList.innerHTML = '';

            tables.forEach(table => {
                const tableItem = document.createElement('div');
                tableItem.className = 'db-table-item';
                tableItem.textContent = table;
                tableItem.onclick = () => dbSelectTable(table, tableItem);
                tableList.appendChild(tableItem);
            });
        }

        async function dbSelectTable(tableName, element) {
            // Update active state
            document.querySelectorAll('.db-table-item').forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');

            dbCurrentTable = tableName;
            document.getElementById('dbContentHeader').textContent = `Table: ${tableName}`;
            document.getElementById('dbTableContainer').innerHTML = '<div style="text-align: center; padding: 60px 32px; color: #666; font-size: 14px;">Loading data...</div>';

            try {
                const response = await fetch(`/api/db/table/${tableName}`);
                const result = await response.json();

                if (result.code === 0) {
                    dbAllTableData = result.data;
                    dbCurrentPage = 1;
                    dbSortConfig = { column: null, direction: 'asc' };
                    dbDisplayTableData();
                } else {
                    dbShowError('Failed to load table data: ' + result.message);
                }
            } catch (error) {
                dbShowError('Error loading table data: ' + error.message);
            }
        }

        async function dbExecuteQuery() {
            const sql = document.getElementById('dbSqlInput').value.trim();
            if (!sql) {
                alert('Please enter a SQL query');
                return;
            }

            document.getElementById('dbContentHeader').textContent = 'Query Results';
            document.getElementById('dbTableContainer').innerHTML = '<div style="text-align: center; padding: 60px 32px; color: #666; font-size: 14px;">Executing query...</div>';

            // Clear table selection
            document.querySelectorAll('.db-table-item').forEach(item => {
                item.classList.remove('active');
            });
            dbCurrentTable = null;

            try {
                const response = await fetch('/api/db/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ sql: sql })
                });

                const result = await response.json();

                if (result.code === 0) {
                    dbAllTableData = result.data;
                    dbCurrentPage = 1;
                    dbSortConfig = { column: null, direction: 'asc' };
                    dbDisplayTableData();
                } else {
                    dbShowError('Query failed: ' + result.message);
                }
            } catch (error) {
                dbShowError('Error executing query: ' + error.message);
            }
        }

        function dbDisplayTableData() {
            const container = document.getElementById('dbTableContainer');
            const paginationDiv = document.getElementById('dbPagination');

            if (!dbAllTableData || !dbAllTableData.rows || dbAllTableData.rows.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 80px 32px; color: #666;"><h3 style="margin-bottom: 12px; color: #000; font-size: 18px; font-weight: 600;">No Data</h3><p style="font-size: 14px; line-height: 1.5;">The table is empty or query returned no results.</p></div>';
                paginationDiv.style.display = 'none';
                return;
            }

            // Apply sorting if configured
            let sortedData = [...dbAllTableData.rows];
            if (dbSortConfig.column !== null) {
                const columnIndex = dbSortConfig.column;
                sortedData.sort((a, b) => {
                    let aVal = a[columnIndex];
                    let bVal = b[columnIndex];

                    // Handle null values
                    if (aVal === null && bVal === null) return 0;
                    if (aVal === null) return dbSortConfig.direction === 'asc' ? -1 : 1;
                    if (bVal === null) return dbSortConfig.direction === 'asc' ? 1 : -1;

                    // Convert to strings for comparison
                    aVal = String(aVal).toLowerCase();
                    bVal = String(bVal).toLowerCase();

                    // Try to parse as numbers if possible
                    const aNum = parseFloat(aVal);
                    const bNum = parseFloat(bVal);
                    if (!isNaN(aNum) && !isNaN(bNum)) {
                        aVal = aNum;
                        bVal = bNum;
                    }

                    if (aVal < bVal) return dbSortConfig.direction === 'asc' ? -1 : 1;
                    if (aVal > bVal) return dbSortConfig.direction === 'asc' ? 1 : -1;
                    return 0;
                });
            }

            // Calculate pagination
            const totalRows = sortedData.length;
            dbTotalPages = Math.ceil(totalRows / dbPageSize);
            const startIndex = (dbCurrentPage - 1) * dbPageSize;
            const endIndex = Math.min(startIndex + dbPageSize, totalRows);
            const pageData = sortedData.slice(startIndex, endIndex);

            // Build table HTML
            let html = '<table class="db-data-table"><thead><tr>';

            // Add column headers with sort indicators
            dbAllTableData.columns.forEach((column, index) => {
                const sortIndicator = dbSortConfig.column === index ?
                    `<span class="db-sort-indicator ${dbSortConfig.direction}"></span>` :
                    '<span class="db-sort-indicator"></span>';
                html += `<th onclick="dbSortTable(${index})">${dbEscapeHtml(column)}${sortIndicator}</th>`;
            });
            html += '</tr></thead><tbody>';

            // Add data rows for current page
            pageData.forEach(row => {
                html += '<tr>';
                row.forEach(cell => {
                    const cellValue = cell === null ? '<em>NULL</em>' : dbEscapeHtml(String(cell));
                    html += `<td>${cellValue}</td>`;
                });
                html += '</tr>';
            });

            html += '</tbody></table>';
            container.innerHTML = html;

            // Update pagination
            dbUpdatePagination(totalRows, startIndex + 1, endIndex);
        }

        function dbSortTable(columnIndex) {
            if (dbSortConfig.column === columnIndex) {
                // Toggle direction if same column
                dbSortConfig.direction = dbSortConfig.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // New column, start with ascending
                dbSortConfig.column = columnIndex;
                dbSortConfig.direction = 'asc';
            }

            // Reset to first page when sorting
            dbCurrentPage = 1;
            dbDisplayTableData();
        }

        function dbUpdatePagination(totalRows, startRow, endRow) {
            const paginationDiv = document.getElementById('dbPagination');
            const paginationInfo = document.getElementById('dbPaginationInfo');
            const pageInput = document.getElementById('dbPageInput');
            const totalPagesSpan = document.getElementById('dbTotalPages');
            const firstPageBtn = document.getElementById('dbFirstPageBtn');
            const prevPageBtn = document.getElementById('dbPrevPageBtn');
            const nextPageBtn = document.getElementById('dbNextPageBtn');
            const lastPageBtn = document.getElementById('dbLastPageBtn');

            if (totalRows === 0) {
                paginationDiv.style.display = 'none';
                return;
            }

            paginationDiv.style.display = 'flex';
            paginationInfo.textContent = `Showing ${startRow}-${endRow} of ${totalRows} records`;
            pageInput.value = dbCurrentPage;
            totalPagesSpan.textContent = dbTotalPages;

            // Update button states
            firstPageBtn.disabled = dbCurrentPage === 1;
            prevPageBtn.disabled = dbCurrentPage === 1;
            nextPageBtn.disabled = dbCurrentPage === dbTotalPages;
            lastPageBtn.disabled = dbCurrentPage === dbTotalPages;
        }

        function dbGoToPage(page) {
            if (page < 1 || page > dbTotalPages) return;
            dbCurrentPage = page;
            dbDisplayTableData();
        }

        function dbGoToPageInput() {
            const pageInput = document.getElementById('dbPageInput');
            const page = parseInt(pageInput.value);
            if (!isNaN(page)) {
                dbGoToPage(page);
            }
        }

        function dbHandlePageInputKeypress(event) {
            if (event.key === 'Enter') {
                dbGoToPageInput();
            }
        }

        function dbShowError(message) {
            const container = document.getElementById('dbTableContainer');
            const paginationDiv = document.getElementById('dbPagination');
            container.innerHTML = `<div class="db-error">${dbEscapeHtml(message)}</div>`;
            paginationDiv.style.display = 'none';
        }

        function dbEscapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>

</html>