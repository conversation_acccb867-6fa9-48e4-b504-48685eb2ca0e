#include "controllers/StaticController.hpp"
#include "core/Application.hpp"
#include "services/AuthService.hpp"
#include <spdlog/spdlog.h>

StaticController::StaticController(Application &app)
    : app_(app)
{
    auto &server = app.getServer();
    
    // Serve index.html at root with authentication check
    server.Get("/", [this](const httplib::Request &req, httplib::Response &res) { 
        handleRoot(req, res); 
    });

    // Serve static files - but exclude API paths
    server.Get(R"(/(.+))", [this](const httplib::Request &req, httplib::Response &res) {
        handleStaticFile(req, res);
    });
}

void StaticController::handleRoot(const httplib::Request &req, httplib::Response &res)
{
    if (isAuthenticated(req))
    {
        serveFile("/index.html", res);
    }
    else
    {
        serveLoginPage(res);
    }
}

void StaticController::handleStaticFile(const httplib::Request &req, httplib::Response &res)
{
    auto matches = req.matches;
    std::string path = matches[1];

    // Don't serve API paths as static files
    if (path.substr(0, 4) == "api/")
    {
        res.status = 404;
        res.body = "API endpoint not found";
        return;
    }

    // Allow access to login.html without authentication
    if (path == "login.html")
    {
        serveFile("/" + path, res);
        return;
    }

    // Check authentication for other files
    if (!isAuthenticated(req))
    {
        res.status = 401;
        res.set_header("Content-Type", "text/html");
        res.body = R"(
<!DOCTYPE html>
<html>
<head>
    <title>Unauthorized</title>
</head>
<body>
    <h1>401 - Unauthorized</h1>
    <p>Please <a href="/">login</a> to access this resource.</p>
</body>
</html>)";
        return;
    }

    serveFile("/" + path, res);
}

bool StaticController::isAuthenticated(const httplib::Request &req)
{
    std::string token = getSessionToken(req);
    if (token.empty())
    {
        return false;
    }

    auto auth_service = app_.getAuthService();
    return auth_service && auth_service->validateSession(token);
}

std::string StaticController::getSessionToken(const httplib::Request &req)
{
    // Try to get token from cookie
    auto cookie_header = req.get_header_value("Cookie");
    if (!cookie_header.empty())
    {
        size_t pos = cookie_header.find("session_token=");
        if (pos != std::string::npos)
        {
            pos += 14; // length of "session_token="
            size_t end = cookie_header.find(';', pos);
            if (end == std::string::npos)
                end = cookie_header.length();
            return cookie_header.substr(pos, end - pos);
        }
    }

    return "";
}

void StaticController::serveLoginPage(httplib::Response &res)
{
    res.status = 200;
    res.set_header("Content-Type", "text/html");
    res.body = R"(<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Report System - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 0.9rem;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }
        
        .success-message {
            color: #27ae60;
            font-size: 0.9rem;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Daily Report System</h1>
            <p>请输入您的登录凭据</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">登录</button>
            
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
        </form>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            // Reset messages
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            // Disable button
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                
                if (result.code === 0) {
                    successMessage.textContent = '登录成功，正在跳转...';
                    successMessage.style.display = 'block';
                    
                    // Redirect to main page after successful login
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    errorMessage.textContent = result.message || '登录失败';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = '网络错误，请重试';
                errorMessage.style.display = 'block';
            } finally {
                // Re-enable button
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
    </script>
</body>
</html>)";
}
